/**
 * REMAN ERP - JavaScript Functionality
 * Handles navigation, interactions, and dynamic content
 */

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize the application
 */
function initializeApp() {
    setupNavigation();
    setupMobileMenu();
    setupMastersDropdown();
    setupHeaderInteractions();
    setupAdvancedSearch();
    setupNewPartsMaster();
    setupButtonInteractions();
    setupFormInteractions();
    setupAccessibility();

    // Show initial section
    showSection('records');

    // Add global debugging function for tabs
    window.debugCoreTabs = function() {
        console.log('=== Core Parts Tabs Debug ===');
        const corePartsMaster = document.querySelector('.core-parts-master');
        console.log('Core parts master found:', !!corePartsMaster);

        if (corePartsMaster) {
            const tabs = ['core-details', 'bom-details', 'operation-details', 'grn-issue-details'];
            tabs.forEach(tabId => {
                const tab = document.getElementById(tabId);
                console.log(`Tab ${tabId}:`, {
                    found: !!tab,
                    display: tab ? tab.style.display : 'N/A',
                    hasActiveClass: tab ? tab.classList.contains('active') : false,
                    innerHTML: tab ? tab.innerHTML.substring(0, 100) + '...' : 'N/A'
                });
            });

            const tabButtons = document.querySelectorAll('.core-parts-master .tab-btn');
            console.log('Tab buttons found:', tabButtons.length);
            tabButtons.forEach((btn, index) => {
                console.log(`Button ${index}:`, {
                    text: btn.textContent,
                    hasActiveClass: btn.classList.contains('active'),
                    onclick: btn.getAttribute('onclick')
                });
            });
        }
        console.log('=== End Debug ===');
    };

    console.log('REMAN ERP Application Initialized');
    console.log('Use window.debugCoreTabs() to debug core parts tabs');
}

/**
 * Setup main navigation functionality
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link:not(#mastersNavLink)');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetSection = this.getAttribute('data-section');

            // Update active nav link
            navLinks.forEach(navLink => navLink.classList.remove('active'));
            this.classList.add('active');

            // Also remove active from Masters nav link
            const mastersNavLink = document.getElementById('mastersNavLink');
            if (mastersNavLink) {
                mastersNavLink.classList.remove('active');
            }

            // Show target section
            showSection(targetSection);

            // Close mobile menu if open
            closeMobileMenu();

            // Hide Masters dropdown if open
            hideMastersDropdown();

            // Smooth scroll to top
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    });
}

/**
 * Setup mobile menu functionality
 */
function setupMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mainNav = document.getElementById('mainNav');

    if (mobileMenuToggle && mainNav) {
        mobileMenuToggle.addEventListener('click', function() {
            toggleMobileMenu();
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenuToggle.contains(e.target) && !mainNav.contains(e.target)) {
                closeMobileMenu();
            }
        });

        // Close mobile menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });
    }
}

/**
 * Toggle mobile menu
 */
function toggleMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mainNav = document.getElementById('mainNav');

    mobileMenuToggle.classList.toggle('active');
    mainNav.classList.toggle('active');

    // Update aria-expanded attribute for accessibility
    const isExpanded = mainNav.classList.contains('active');
    mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
}

/**
 * Close mobile menu
 */
function closeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mainNav = document.getElementById('mainNav');

    mobileMenuToggle.classList.remove('active');
    mainNav.classList.remove('active');
    mobileMenuToggle.setAttribute('aria-expanded', 'false');
}

/**
 * Setup Masters dropdown functionality
 */
function setupMastersDropdown() {
    setupMastersHoverInteractions();
    setupMastersSearch();
    setupMastersDropdownItems();
    setupMastersKeyboardNavigation();
}

/**
 * Setup Masters dropdown hover interactions
 */
function setupMastersHoverInteractions() {
    const mastersNavItem = document.getElementById('mastersNavItem');
    const mastersDropdown = document.getElementById('mastersDropdown');
    const mastersNavLink = document.getElementById('mastersNavLink');

    if (mastersNavItem && mastersDropdown) {
        let hoverTimeout;

        // Show dropdown on hover
        mastersNavItem.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
            showMastersDropdown();
        });

        // Hide dropdown with delay when leaving - increased delay for better UX
        mastersNavItem.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(() => {
                hideMastersDropdown();
            }, 500); // 500ms delay for easier navigation
        });

        // Keep dropdown open when hovering over it
        mastersDropdown.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
        });

        mastersDropdown.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(() => {
                hideMastersDropdown();
            }, 300); // Slightly longer delay when leaving dropdown
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!mastersNavItem.contains(e.target) && !mastersDropdown.contains(e.target)) {
                hideMastersDropdown();
            }
        });

        // Handle Masters nav link click - ONLY show dropdown, don't navigate
        mastersNavLink.addEventListener('click', function(e) {
            e.preventDefault();

            // On both mobile and desktop, just toggle dropdown visibility
            if (window.innerWidth <= 768) {
                toggleMastersDropdownMobile();
            } else {
                // On desktop, toggle dropdown (don't navigate to section)
                toggleMastersDropdownDesktop();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!mastersNavItem.contains(e.target)) {
                hideMastersDropdown();
            }
        });
    }
}

/**
 * Setup Masters search functionality
 */
function setupMastersSearch() {
    const searchInput = document.getElementById('mastersSearchInput');
    const searchClear = document.getElementById('mastersSearchClear');

    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.trim();
            filterMastersItems(searchTerm);

            // Show/hide clear button
            if (searchClear) {
                searchClear.style.display = searchTerm ? 'flex' : 'none';
            }
        });

        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                clearMastersSearch();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                focusFirstVisibleItem();
            }
        });
    }

    if (searchClear) {
        searchClear.addEventListener('click', function() {
            clearMastersSearch();
        });
    }
}

/**
 * Setup Masters dropdown item interactions
 */
function setupMastersDropdownItems() {
    const dropdownItems = document.querySelectorAll('.masters-dropdown-item');

    dropdownItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            const targetSubmenu = this.getAttribute('data-submenu');

            // Navigate to Masters section and show specific submenu
            navigateToMastersSection(targetSubmenu);

            // Hide dropdown
            hideMastersDropdown();

            // Close mobile menu if open
            closeMobileMenu();
        });
    });
}

/**
 * Setup keyboard navigation for Masters dropdown
 */
function setupMastersKeyboardNavigation() {
    const mastersDropdown = document.getElementById('mastersDropdown');

    if (mastersDropdown) {
        mastersDropdown.addEventListener('keydown', function(e) {
            const visibleItems = Array.from(mastersDropdown.querySelectorAll('.masters-dropdown-item:not([style*="display: none"])'));
            const currentFocus = document.activeElement;
            const currentIndex = visibleItems.indexOf(currentFocus);

            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = currentIndex < visibleItems.length - 1 ? currentIndex + 1 : 0;
                    visibleItems[nextIndex]?.focus();
                    break;

                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : visibleItems.length - 1;
                    visibleItems[prevIndex]?.focus();
                    break;

                case 'Escape':
                    hideMastersDropdown();
                    document.getElementById('mastersNavLink')?.focus();
                    break;

                case 'Enter':
                    if (currentFocus && currentFocus.classList.contains('masters-dropdown-item')) {
                        currentFocus.click();
                    }
                    break;
            }
        });
    }
}

/**
 * Show Masters dropdown
 */
function showMastersDropdown() {
    const mastersDropdown = document.getElementById('mastersDropdown');
    const mastersNavLink = document.getElementById('mastersNavLink');

    if (mastersDropdown) {
        mastersDropdown.classList.add('show');
    }

    // Add visual indicator to Masters nav link when dropdown is open
    if (mastersNavLink) {
        mastersNavLink.classList.add('dropdown-open');
    }
}

/**
 * Hide Masters dropdown
 */
function hideMastersDropdown() {
    const mastersDropdown = document.getElementById('mastersDropdown');
    const mastersNavLink = document.getElementById('mastersNavLink');

    if (mastersDropdown) {
        mastersDropdown.classList.remove('show');
        mastersDropdown.classList.remove('mobile-active');
    }

    // Remove visual indicator from Masters nav link
    if (mastersNavLink) {
        mastersNavLink.classList.remove('dropdown-open');
    }
}

/**
 * Toggle Masters dropdown for mobile
 */
function toggleMastersDropdownMobile() {
    const mastersDropdown = document.getElementById('mastersDropdown');
    if (mastersDropdown) {
        mastersDropdown.classList.toggle('mobile-active');
    }
}

/**
 * Toggle Masters dropdown for desktop
 */
function toggleMastersDropdownDesktop() {
    const mastersDropdown = document.getElementById('mastersDropdown');
    if (mastersDropdown) {
        const isVisible = mastersDropdown.classList.contains('show');

        if (isVisible) {
            hideMastersDropdown();
        } else {
            showMastersDropdown();
        }
    }
}

/**
 * Navigate to Masters section with optional submenu
 */
function navigateToMastersSection(submenuId = 'bin-location') {
    // Update active nav link
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(navLink => navLink.classList.remove('active'));

    const mastersNavLink = document.getElementById('mastersNavLink');
    if (mastersNavLink) {
        mastersNavLink.classList.add('active');
    }

    // Show Masters section
    showSection('masters');

    // Show specific submenu content
    showSubmenuContent(submenuId);

    // If navigating to parts, setup parts functionality
    if (submenuId === 'parts') {
        setupPartsManagement();
    }

    // Smooth scroll to top
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

/**
 * Filter Masters dropdown items based on search term
 */
function filterMastersItems(searchTerm) {
    const dropdownItems = document.querySelectorAll('.masters-dropdown-item');
    const noResults = document.getElementById('mastersNoResults');
    const dropdownGrid = document.getElementById('mastersDropdownGrid');

    let visibleCount = 0;

    dropdownItems.forEach(item => {
        const text = item.querySelector('.masters-item-text').textContent.toLowerCase();
        const matches = text.includes(searchTerm.toLowerCase());

        item.style.display = matches ? 'block' : 'none';
        if (matches) visibleCount++;
    });

    // Show/hide no results message
    if (noResults && dropdownGrid) {
        if (visibleCount === 0 && searchTerm.length > 0) {
            dropdownGrid.style.display = 'none';
            noResults.style.display = 'block';
        } else {
            dropdownGrid.style.display = 'grid';
            noResults.style.display = 'none';
        }
    }
}

/**
 * Clear Masters search
 */
function clearMastersSearch() {
    const searchInput = document.getElementById('mastersSearchInput');
    const searchClear = document.getElementById('mastersSearchClear');

    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }

    if (searchClear) {
        searchClear.style.display = 'none';
    }

    // Reset filter
    filterMastersItems('');
}

/**
 * Focus first visible Masters dropdown item
 */
function focusFirstVisibleItem() {
    const visibleItems = document.querySelectorAll('.masters-dropdown-item:not([style*="display: none"])');
    if (visibleItems.length > 0) {
        visibleItems[0].focus();
    }
}

/**
 * Show specific submenu content and hide others
 */
function showSubmenuContent(submenuId) {
    const submenuContents = document.querySelectorAll('.submenu-content');

    submenuContents.forEach(content => {
        content.classList.remove('active');
    });

    const targetContent = document.getElementById(submenuId);
    if (targetContent) {
        targetContent.classList.add('active');

        // Update page title for submenu
        updateSubmenuPageTitle(submenuId);
    }
}

/**
 * Update page title for submenu
 */
function updateSubmenuPageTitle(submenuId) {
    const submenuTitles = {
        'bin-location': 'Bin Location Master',
        'category': 'Category Master',
        'sac-hsn': 'SAC & HSN / Custom Tariff Master',
        'supplier-order-class': 'Supplier Order Class',
        'parts': 'Parts Master',
        'supplier': 'Supplier Master',
        'clearing-agent': 'Clearing Agent Master',
        'dealer': 'Dealer Master',
        'service-technician': 'Service Technician Master',
        'renovator': 'Renovator Master',
        'customer-master': 'Customer Master',
        'operation': 'Operation Master',
        'machining-checklist': 'Machining Checklist',
        'machining-checklist-vendor': 'Machining Checklist Vendor Association'
    };

    const title = submenuTitles[submenuId] || 'Master Data';
    document.title = `${title} - REMAN ERP`;
}

/**
 * Show specific section and hide others
 */
function showSection(sectionId) {
    const sections = document.querySelectorAll('.content-section');

    sections.forEach(section => {
        section.classList.remove('active');
    });

    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');

        // Update page title
        updatePageTitle(sectionId);

        // Trigger section-specific initialization
        initializeSection(sectionId);
    }
}

/**
 * Setup Advanced Search functionality
 */
function setupAdvancedSearch() {
    setupSearchTypeSelector();
    setupAdvancedSearchInput();
    setupSearchActions();
    setupSearchDropdown();
    setupSearchKeyboardShortcuts();
}

/**
 * Enhanced Search Type Selector with Large Dataset Support
 */
function setupSearchTypeSelector() {
    const searchTypeBtn = document.getElementById('searchTypeBtn');
    const searchTypeDropdown = document.getElementById('searchTypeDropdown');
    const searchTypeOptions = document.querySelectorAll('.search-type-option');
    const searchTypeFilter = document.getElementById('searchTypeFilter');
    const clearFilterBtn = document.getElementById('clearSearchTypeFilter');

    // Enhanced dropdown state
    let currentPage = 1;
    let pageSize = 10;
    let filteredOptions = Array.from(searchTypeOptions);
    let highlightedIndex = -1;

    if (searchTypeBtn && searchTypeDropdown) {
        // Toggle dropdown
        searchTypeBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleSearchTypeDropdown();
        });

        // Handle option selection
        searchTypeOptions.forEach(option => {
            option.addEventListener('click', function() {
                selectSearchType(this);
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchTypeBtn.contains(e.target) && !searchTypeDropdown.contains(e.target)) {
                closeSearchTypeDropdown();
            }
        });

        // Keyboard navigation
        searchTypeBtn.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowDown' || e.key === 'Enter') {
                e.preventDefault();
                toggleSearchTypeDropdown();
                if (searchTypeDropdown.classList.contains('active')) {
                    searchTypeOptions[0]?.focus();
                }
            }
        });

        searchTypeDropdown.addEventListener('keydown', function(e) {
            const options = Array.from(searchTypeOptions);
            const currentIndex = options.indexOf(document.activeElement);

            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
                    options[nextIndex]?.focus();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
                    options[prevIndex]?.focus();
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (document.activeElement.classList.contains('search-type-option')) {
                        selectSearchType(document.activeElement);
                    }
                    break;
                case 'Escape':
                    closeSearchTypeDropdown();
                    searchTypeBtn.focus();
                    break;
            }
        });

        // Enhanced search functionality
        if (searchTypeFilter) {
            let searchTimeout;
            searchTypeFilter.addEventListener('input', function(e) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    filterSearchTypeOptions(e.target.value);
                }, 150); // Debounced search
            });

            searchTypeFilter.addEventListener('keydown', function(e) {
                handleEnhancedKeyboard(e);
            });
        }

        // Clear filter functionality
        if (clearFilterBtn) {
            clearFilterBtn.addEventListener('click', function() {
                clearSearchTypeFilter();
            });
        }

        // Page size selector
        const pageSizeSelect = document.getElementById('searchTypePageSize');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', function() {
                pageSize = parseInt(this.value);
                currentPage = 1; // Reset to first page
                updatePagination();
            });
        }

        // Initialize enhanced features
        initializeEnhancedDropdown();
    }

    /**
     * Initialize enhanced dropdown functionality
     */
    function initializeEnhancedDropdown() {
        updatePagination();
        setupVirtualScrolling();
    }

    /**
     * Filter search type options with enhanced search
     */
    function filterSearchTypeOptions(searchTerm) {
        const allOptions = Array.from(searchTypeOptions);
        const searchLower = searchTerm.toLowerCase();

        filteredOptions = allOptions.filter(option => {
            const text = option.textContent.toLowerCase();
            const matches = text.includes(searchLower);
            option.style.display = matches ? 'flex' : 'none';
            return matches;
        });

        // Reset to first page when filtering
        currentPage = 1;
        highlightedIndex = -1;

        updatePagination();
        updateNoResultsMessage(searchTerm);
    }

    /**
     * Clear search type filter
     */
    function clearSearchTypeFilter() {
        if (searchTypeFilter) {
            searchTypeFilter.value = '';
            filterSearchTypeOptions('');
            searchTypeFilter.focus();
        }
    }

    /**
     * Update pagination controls
     */
    function updatePagination() {
        const paginationContainer = document.getElementById('searchTypePagination');
        const paginationInfo = document.getElementById('searchTypePaginationInfo');
        const paginationPages = document.getElementById('searchTypePaginationPages');
        const prevBtn = document.getElementById('searchTypePrevBtn');
        const nextBtn = document.getElementById('searchTypeNextBtn');

        if (!paginationContainer) return;

        const totalItems = filteredOptions.length;
        const totalPages = Math.ceil(totalItems / pageSize);
        const startItem = (currentPage - 1) * pageSize + 1;
        const endItem = Math.min(currentPage * pageSize, totalItems);

        // Show/hide pagination based on need
        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // Update pagination info
        if (paginationInfo) {
            paginationInfo.textContent = `Showing ${startItem}-${endItem} of ${totalItems}`;
        }

        if (paginationPages) {
            paginationPages.textContent = `${currentPage} / ${totalPages}`;
        }

        // Update button states
        if (prevBtn) {
            prevBtn.disabled = currentPage <= 1;
            prevBtn.onclick = () => {
                if (currentPage > 1) {
                    currentPage--;
                    updatePagination();
                    renderCurrentPage();
                }
            };
        }

        if (nextBtn) {
            nextBtn.disabled = currentPage >= totalPages;
            nextBtn.onclick = () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    updatePagination();
                    renderCurrentPage();
                }
            };
        }

        renderCurrentPage();
    }

    /**
     * Render current page items
     */
    function renderCurrentPage() {
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;

        filteredOptions.forEach((option, index) => {
            if (index >= startIndex && index < endIndex) {
                option.style.display = 'flex';
            } else {
                option.style.display = 'none';
            }
        });
    }

    /**
     * Setup virtual scrolling for large datasets
     */
    function setupVirtualScrolling() {
        const optionsContainer = document.getElementById('searchTypeOptionsContainer');
        if (!optionsContainer || filteredOptions.length < 100) return;

        // Virtual scrolling implementation for very large datasets
        const itemHeight = 44; // Height of each option
        const containerHeight = optionsContainer.clientHeight;
        const visibleItems = Math.ceil(containerHeight / itemHeight) + 2; // Buffer items

        let scrollTop = 0;

        optionsContainer.addEventListener('scroll', function() {
            scrollTop = this.scrollTop;
            const startIndex = Math.floor(scrollTop / itemHeight);
            const endIndex = Math.min(startIndex + visibleItems, filteredOptions.length);

            // Hide all items first
            filteredOptions.forEach(option => option.style.display = 'none');

            // Show only visible items
            for (let i = startIndex; i < endIndex; i++) {
                if (filteredOptions[i]) {
                    filteredOptions[i].style.display = 'flex';
                    filteredOptions[i].style.transform = `translateY(${i * itemHeight}px)`;
                }
            }
        });
    }

    /**
     * Update no results message
     */
    function updateNoResultsMessage(searchTerm) {
        const noResults = document.getElementById('searchTypeNoResults');
        const optionsContainer = document.getElementById('searchTypeOptionsContainer');

        if (!noResults || !optionsContainer) return;

        if (filteredOptions.length === 0 && searchTerm.length > 0) {
            optionsContainer.style.display = 'none';
            noResults.style.display = 'block';
        } else {
            optionsContainer.style.display = 'block';
            noResults.style.display = 'none';
        }
    }

    /**
     * Enhanced keyboard navigation
     */
    function handleEnhancedKeyboard(e) {
        const visibleOptions = filteredOptions.filter(option =>
            option.style.display !== 'none'
        );

        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                highlightedIndex = Math.min(highlightedIndex + 1, visibleOptions.length - 1);
                updateHighlight(visibleOptions);
                break;
            case 'ArrowUp':
                e.preventDefault();
                highlightedIndex = Math.max(highlightedIndex - 1, -1);
                updateHighlight(visibleOptions);
                break;
            case 'Enter':
                e.preventDefault();
                if (highlightedIndex >= 0 && visibleOptions[highlightedIndex]) {
                    selectSearchType(visibleOptions[highlightedIndex]);
                }
                break;
            case 'Escape':
                clearSearchTypeFilter();
                closeSearchTypeDropdown();
                break;
        }
    }

    /**
     * Update visual highlight for keyboard navigation
     */
    function updateHighlight(visibleOptions) {
        // Remove previous highlights
        visibleOptions.forEach(option => option.classList.remove('highlighted'));

        // Add highlight to current option
        if (highlightedIndex >= 0 && visibleOptions[highlightedIndex]) {
            visibleOptions[highlightedIndex].classList.add('highlighted');
            visibleOptions[highlightedIndex].scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    }
}

/**
 * Setup Advanced Search Input
 */
function setupAdvancedSearchInput() {
    const searchInput = document.getElementById('headerAdvancedSearch');
    const searchDropdown = document.getElementById('advancedSearchDropdown');
    const searchStatusText = document.getElementById('searchStatusText');
    const searchResultsCount = document.getElementById('searchResultsCount');

    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function(e) {
            const query = e.target.value.trim();

            // Clear previous timeout
            clearTimeout(searchTimeout);

            // Show/hide clear button
            toggleClearButton(query.length > 0);

            if (query.length > 0) {
                // Show dropdown
                showAdvancedSearchDropdown();

                // Update status
                searchStatusText.textContent = 'Searching...';
                searchResultsCount.style.display = 'none';

                // Debounced search
                searchTimeout = setTimeout(() => {
                    performAdvancedSearch(query);
                }, 300);
            } else {
                // Show default content
                showDefaultSearchContent();
                searchStatusText.textContent = 'Start typing to search...';
                searchResultsCount.style.display = 'none';
            }
        });

        searchInput.addEventListener('focus', function() {
            showAdvancedSearchDropdown();
        });

        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideAdvancedSearchDropdown();
                searchInput.blur();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                focusFirstSearchResult();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            const searchContainer = document.querySelector('.advanced-search-container');
            if (!searchContainer.contains(e.target)) {
                hideAdvancedSearchDropdown();
            }
        });
    }
}

/**
 * Setup Search Actions
 */
function setupSearchActions() {
    const voiceSearchBtn = document.getElementById('voiceSearchBtn');
    const filtersBtn = document.getElementById('filtersBtn');
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    const searchSubmitBtn = document.getElementById('searchSubmitBtn');
    const searchInput = document.getElementById('headerAdvancedSearch');

    // Voice search
    if (voiceSearchBtn) {
        voiceSearchBtn.addEventListener('click', function() {
            toggleVoiceSearch();
        });
    }

    // Filters
    if (filtersBtn) {
        filtersBtn.addEventListener('click', function() {
            showAdvancedFilters();
        });
    }

    // Clear search
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', function() {
            clearAdvancedSearch();
        });
    }

    // Submit search
    if (searchSubmitBtn) {
        searchSubmitBtn.addEventListener('click', function() {
            submitAdvancedSearch();
        });
    }

    // Enter key submission
    if (searchInput) {
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                submitAdvancedSearch();
            }
        });
    }
}

/**
 * Setup Search Dropdown
 */
function setupSearchDropdown() {
    const searchResultItems = document.querySelectorAll('.search-result-item');
    const advancedSearchLink = document.getElementById('advancedSearchLink');

    // Handle result item clicks
    searchResultItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            handleSearchResultClick(this);
        });

        // Keyboard navigation
        item.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleSearchResultClick(this);
            }
        });
    });

    // Advanced search link
    if (advancedSearchLink) {
        advancedSearchLink.addEventListener('click', function() {
            openAdvancedSearchModal();
        });
    }
}

/**
 * Setup Search Keyboard Shortcuts
 */
function setupSearchKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+K or Cmd+K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            focusAdvancedSearch();
        }

        // Ctrl+/ to show search shortcuts
        if ((e.ctrlKey || e.metaKey) && e.key === '/') {
            e.preventDefault();
            showSearchShortcuts();
        }
    });
}

/**
 * Update page title based on active section
 */
function updatePageTitle(sectionId) {
    const sectionTitles = {
        'records': 'Records Management',
        'edit': 'Edit Operations',
        'masters': 'Master Data',
        'transactions': 'Transactions',
        'status': 'System Status',
        'reports': 'Reports & Analytics',
        'options': 'System Options',
        'window': 'Window Management',
        'help': 'Help & Support'
    };

    const title = sectionTitles[sectionId] || 'REMAN ERP';
    document.title = `${title} - REMAN ERP`;
}

/**
 * Advanced Search Helper Functions
 */

// Search Type Dropdown Functions
function toggleSearchTypeDropdown() {
    const dropdown = document.getElementById('searchTypeDropdown');
    const btn = document.getElementById('searchTypeBtn');

    if (dropdown && btn) {
        const isActive = dropdown.classList.contains('active');
        if (isActive) {
            closeSearchTypeDropdown();
        } else {
            openSearchTypeDropdown();
        }
    }
}

function openSearchTypeDropdown() {
    const dropdown = document.getElementById('searchTypeDropdown');
    const btn = document.getElementById('searchTypeBtn');

    if (dropdown && btn) {
        dropdown.classList.add('active');
        btn.setAttribute('aria-expanded', 'true');
    }
}

function closeSearchTypeDropdown() {
    const dropdown = document.getElementById('searchTypeDropdown');
    const btn = document.getElementById('searchTypeBtn');

    if (dropdown && btn) {
        dropdown.classList.remove('active');
        btn.setAttribute('aria-expanded', 'false');
    }
}

function selectSearchType(option) {
    const btn = document.getElementById('searchTypeBtn');
    const typeText = btn.querySelector('.search-type-text');
    const typeIcon = btn.querySelector('.search-type-icon');
    const searchInput = document.getElementById('headerAdvancedSearch');

    // Update active state
    document.querySelectorAll('.search-type-option').forEach(opt => opt.classList.remove('active'));
    option.classList.add('active');

    // Update button text and icon
    const newText = option.querySelector('span').textContent;
    const newIcon = option.querySelector('svg').cloneNode(true);

    typeText.textContent = newText;
    typeIcon.replaceWith(newIcon);
    newIcon.classList.add('search-type-icon');

    // Update placeholder
    const type = option.dataset.type;
    const placeholders = {
        'all': 'Search across all modules...',
        'customers': 'Search customers...',
        'parts': 'Search parts and inventory...',
        'transactions': 'Search transactions...',
        'reports': 'Search reports...'
    };

    if (searchInput) {
        searchInput.placeholder = placeholders[type] || 'Search...';
    }

    closeSearchTypeDropdown();

    // Trigger search if there's existing input
    if (searchInput && searchInput.value.trim()) {
        performAdvancedSearch(searchInput.value.trim());
    }
}

// Search Dropdown Functions
function showAdvancedSearchDropdown() {
    const dropdown = document.getElementById('advancedSearchDropdown');
    const searchInput = document.getElementById('headerAdvancedSearch');

    if (dropdown && searchInput) {
        dropdown.classList.add('active');
        searchInput.setAttribute('aria-expanded', 'true');
    }
}

function hideAdvancedSearchDropdown() {
    const dropdown = document.getElementById('advancedSearchDropdown');
    const searchInput = document.getElementById('headerAdvancedSearch');

    if (dropdown && searchInput) {
        dropdown.classList.remove('active');
        searchInput.setAttribute('aria-expanded', 'false');
    }
}

function showDefaultSearchContent() {
    const container = document.getElementById('searchResultsContainer');
    if (container) {
        // Show default quick actions and recent searches
        container.innerHTML = `
            <div class="search-section quick-actions-section">
                <h4 class="search-section-title">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Quick Actions
                </h4>
                <div class="search-results-list">
                    <a href="#" class="search-result-item quick-action" data-action="new-customer">
                        <div class="result-icon">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                                <line x1="20" y1="8" x2="20" y2="14" stroke="currentColor" stroke-width="2"/>
                                <line x1="17" y1="11" x2="23" y2="11" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="result-content">
                            <div class="result-title">Add New Customer</div>
                            <div class="result-description">Create a new customer record</div>
                        </div>
                        <div class="result-shortcut">
                            <kbd>Ctrl</kbd><kbd>N</kbd>
                        </div>
                    </a>
                    <a href="#" class="search-result-item quick-action" data-action="new-transaction">
                        <div class="result-icon">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <line x1="12" y1="1" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
                                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="result-content">
                            <div class="result-title">New Transaction</div>
                            <div class="result-description">Create a new transaction</div>
                        </div>
                        <div class="result-shortcut">
                            <kbd>Ctrl</kbd><kbd>T</kbd>
                        </div>
                    </a>
                </div>
            </div>
        `;
    }
}

// Search Action Functions
function toggleClearButton(show) {
    const clearBtn = document.getElementById('clearSearchBtn');
    if (clearBtn) {
        clearBtn.style.display = show ? 'flex' : 'none';
    }
}

function clearAdvancedSearch() {
    const searchInput = document.getElementById('headerAdvancedSearch');
    const searchStatusText = document.getElementById('searchStatusText');
    const searchResultsCount = document.getElementById('searchResultsCount');

    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }

    if (searchStatusText) {
        searchStatusText.textContent = 'Start typing to search...';
    }

    if (searchResultsCount) {
        searchResultsCount.style.display = 'none';
    }

    toggleClearButton(false);
    showDefaultSearchContent();
}

function submitAdvancedSearch() {
    const searchInput = document.getElementById('headerAdvancedSearch');
    if (searchInput && searchInput.value.trim()) {
        performAdvancedSearch(searchInput.value.trim());
        // In a real app, this would navigate to search results page
        console.log('Submitting search:', searchInput.value.trim());
    }
}

function performAdvancedSearch(query) {
    const searchStatusText = document.getElementById('searchStatusText');
    const searchResultsCount = document.getElementById('searchResultsCount');
    const container = document.getElementById('searchResultsContainer');

    // Simulate search delay
    setTimeout(() => {
        // Mock search results
        const mockResults = generateMockSearchResults(query);

        if (searchStatusText) {
            searchStatusText.textContent = `Search results for "${query}"`;
        }

        if (searchResultsCount) {
            searchResultsCount.textContent = `${mockResults.length} results`;
            searchResultsCount.style.display = 'inline-block';
        }

        if (container) {
            container.innerHTML = generateSearchResultsHTML(mockResults);
        }

        // Re-attach event listeners to new results
        attachSearchResultListeners();
    }, 300);
}

function generateMockSearchResults(query) {
    // Mock search results based on query
    const allResults = [
        {
            type: 'customer',
            title: 'ABC Corporation',
            description: 'Customer ID: CUST001 • Active since 2020',
            icon: 'users',
            action: 'view-customer'
        },
        {
            type: 'part',
            title: 'Engine Block - V8',
            description: 'Part #: ENG-V8-001 • In Stock: 15 units',
            icon: 'package',
            action: 'view-part'
        },
        {
            type: 'transaction',
            title: 'Purchase Order #PO-2024-001',
            description: 'Status: Pending Approval • Amount: $15,250',
            icon: 'activity',
            action: 'view-transaction'
        },
        {
            type: 'report',
            title: 'Monthly Sales Report',
            description: 'Generated: Dec 2024 • 45 pages',
            icon: 'file-text',
            action: 'view-report'
        }
    ];

    // Filter results based on query
    return allResults.filter(result =>
        result.title.toLowerCase().includes(query.toLowerCase()) ||
        result.description.toLowerCase().includes(query.toLowerCase())
    );
}

function generateSearchResultsHTML(results) {
    if (results.length === 0) {
        return `
            <div class="search-section">
                <div class="search-no-results">
                    <p>No results found. Try adjusting your search terms.</p>
                </div>
            </div>
        `;
    }

    return `
        <div class="search-section search-results-section">
            <h4 class="search-section-title">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                    <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                </svg>
                Search Results
            </h4>
            <div class="search-results-list">
                ${results.map(result => `
                    <a href="#" class="search-result-item search-result" data-action="${result.action}">
                        <div class="result-icon">
                            ${getIconSVG(result.icon)}
                        </div>
                        <div class="result-content">
                            <div class="result-title">${result.title}</div>
                            <div class="result-description">${result.description}</div>
                        </div>
                    </a>
                `).join('')}
            </div>
        </div>
    `;
}

function getIconSVG(iconType) {
    const icons = {
        'users': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2"/><circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/></svg>',
        'package': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2"/><path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2"/></svg>',
        'activity': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2"/></svg>',
        'file-text': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/><polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/></svg>'
    };
    return icons[iconType] || icons['file-text'];
}

function attachSearchResultListeners() {
    const searchResultItems = document.querySelectorAll('.search-result-item');

    searchResultItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            handleSearchResultClick(this);
        });
    });
}

function handleSearchResultClick(item) {
    const action = item.dataset.action;
    console.log('Search result clicked:', action);

    // Handle different actions
    switch(action) {
        case 'new-customer':
            // Navigate to new customer form
            break;
        case 'new-transaction':
            // Navigate to new transaction form
            break;
        case 'generate-report':
            // Open report generator
            break;
        case 'view-customer':
        case 'view-part':
        case 'view-transaction':
        case 'view-report':
            // Navigate to respective view
            break;
        default:
            console.log('Unknown action:', action);
    }

    hideAdvancedSearchDropdown();
}

// Additional helper functions
function focusFirstSearchResult() {
    const firstResult = document.querySelector('.search-result-item');
    if (firstResult) {
        firstResult.focus();
    }
}

function focusAdvancedSearch() {
    const searchInput = document.getElementById('headerAdvancedSearch');
    if (searchInput) {
        searchInput.focus();
        showAdvancedSearchDropdown();
    }
}

function toggleVoiceSearch() {
    const voiceBtn = document.getElementById('voiceSearchBtn');

    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';

        recognition.onstart = function() {
            voiceBtn.classList.add('recording');
        };

        recognition.onresult = function(event) {
            const transcript = event.results[0][0].transcript;
            const searchInput = document.getElementById('headerAdvancedSearch');
            if (searchInput) {
                searchInput.value = transcript;
                performAdvancedSearch(transcript);
            }
        };

        recognition.onend = function() {
            voiceBtn.classList.remove('recording');
        };

        recognition.onerror = function(event) {
            console.error('Speech recognition error:', event.error);
            voiceBtn.classList.remove('recording');
        };

        recognition.start();
    } else {
        alert('Speech recognition is not supported in your browser.');
    }
}

function showAdvancedFilters() {
    // Placeholder for advanced filters modal
    console.log('Show advanced filters modal');
}

function openAdvancedSearchModal() {
    // Placeholder for advanced search modal
    console.log('Open advanced search modal');
}

function showSearchShortcuts() {
    // Placeholder for search shortcuts modal
    console.log('Show search shortcuts');
}

/**
 * Initialize section-specific functionality
 */
function initializeSection(sectionId) {
    switch(sectionId) {
        case 'status':
            updateStatusIndicators();
            break;
        case 'options':
            loadUserPreferences();
            break;
        case 'reports':
            initializeReportFilters();
            break;
        case 'masters':
            initializeMastersSection();
            break;
        default:
            break;
    }
}

/**
 * Initialize Masters section with default submenu
 */
function initializeMastersSection() {
    // Show default submenu content (bin-location)
    showSubmenuContent('bin-location');
}

/**
 * Setup header interactions (search, notifications, profile, logout)
 */
function setupHeaderInteractions() {
    setupSearchFunctionality();
    setupNotificationDropdown();
    setupProfileDropdown();
    setupLogoutFunctionality();
    setupClickOutsideHandlers();
}

/**
 * Setup search functionality
 */
function setupSearchFunctionality() {
    const searchInput = document.getElementById('headerSearch');

    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.trim();
            if (searchTerm.length > 2) {
                performSearch(searchTerm);
            }
        }, 300));

        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const searchTerm = e.target.value.trim();
                if (searchTerm) {
                    performSearch(searchTerm);
                }
            }
        });
    }
}

/**
 * Perform search operation
 */
function performSearch(searchTerm) {
    console.log(`Searching for: ${searchTerm}`);
    showNotification(`Searching for "${searchTerm}"...`, 'info');

    // Placeholder for actual search implementation
    // This would typically make an API call to search the system
}

/**
 * Setup notification dropdown
 */
function setupNotificationDropdown() {
    const notificationBtn = document.getElementById('notificationBtn');
    const notificationDropdown = document.getElementById('notificationDropdown');
    const markAllReadBtn = document.getElementById('markAllRead');

    if (notificationBtn && notificationDropdown) {
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleDropdown(notificationDropdown, notificationBtn);
        });

        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', function() {
                markAllNotificationsAsRead();
            });
        }
    }
}

/**
 * Setup profile dropdown
 */
function setupProfileDropdown() {
    const profileBtn = document.getElementById('profileBtn');
    const profileDropdown = document.getElementById('profileDropdown');

    if (profileBtn && profileDropdown) {
        profileBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleDropdown(profileDropdown, profileBtn);
        });

        // Handle profile menu item clicks
        const profileMenuItems = profileDropdown.querySelectorAll('.profile-menu-item');
        profileMenuItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.getAttribute('href').substring(1);
                handleProfileMenuAction(action);
                closeAllDropdowns();
            });
        });
    }
}

/**
 * Setup logout functionality
 */
function setupLogoutFunctionality() {
    const logoutBtn = document.getElementById('logoutBtn');

    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            showLogoutConfirmation();
        });
    }
}

/**
 * Setup click outside handlers for dropdowns
 */
function setupClickOutsideHandlers() {
    document.addEventListener('click', function(e) {
        const notificationDropdown = document.getElementById('notificationDropdown');
        const profileDropdown = document.getElementById('profileDropdown');
        const notificationBtn = document.getElementById('notificationBtn');
        const profileBtn = document.getElementById('profileBtn');

        // Close dropdowns if clicking outside
        if (notificationDropdown && !notificationBtn.contains(e.target) && !notificationDropdown.contains(e.target)) {
            closeDropdown(notificationDropdown, notificationBtn);
        }

        if (profileDropdown && !profileBtn.contains(e.target) && !profileDropdown.contains(e.target)) {
            closeDropdown(profileDropdown, profileBtn);
        }
    });

    // Close dropdowns on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllDropdowns();
        }
    });
}

/**
 * Toggle dropdown visibility
 */
function toggleDropdown(dropdown, button) {
    const isActive = dropdown.classList.contains('active');

    // Close all dropdowns first
    closeAllDropdowns();

    if (!isActive) {
        dropdown.classList.add('active');
        button.setAttribute('aria-expanded', 'true');
    }
}

/**
 * Close specific dropdown
 */
function closeDropdown(dropdown, button) {
    dropdown.classList.remove('active');
    button.setAttribute('aria-expanded', 'false');
}

/**
 * Close all dropdowns
 */
function closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('.notification-dropdown, .profile-dropdown');
    const buttons = document.querySelectorAll('.notification-btn, .profile-btn');

    dropdowns.forEach(dropdown => dropdown.classList.remove('active'));
    buttons.forEach(button => button.setAttribute('aria-expanded', 'false'));
}

/**
 * Mark all notifications as read
 */
function markAllNotificationsAsRead() {
    const unreadNotifications = document.querySelectorAll('.notification-item.unread');
    const notificationBadge = document.getElementById('notificationBadge');

    unreadNotifications.forEach(notification => {
        notification.classList.remove('unread');
    });

    if (notificationBadge) {
        notificationBadge.textContent = '0';
        notificationBadge.style.display = 'none';
    }

    showNotification('All notifications marked as read', 'success');
}

/**
 * Handle profile menu actions
 */
function handleProfileMenuAction(action) {
    switch(action) {
        case 'profile':
            showNotification('Opening user profile...', 'info');
            console.log('Navigate to user profile');
            break;
        case 'settings':
            showNotification('Opening settings...', 'info');
            console.log('Navigate to settings');
            break;
        case 'help':
            showNotification('Opening help center...', 'info');
            console.log('Navigate to help');
            break;
        default:
            console.log(`Unknown profile action: ${action}`);
    }
}

/**
 * Show logout confirmation dialog
 */
function showLogoutConfirmation() {
    const confirmed = confirm('Are you sure you want to logout?');

    if (confirmed) {
        performLogout();
    }
}

/**
 * Perform logout operation
 */
function performLogout() {
    showNotification('Logging out...', 'info');

    // Simulate logout process
    setTimeout(() => {
        console.log('User logged out');
        // In a real application, this would redirect to login page
        // window.location.href = '/login';
        showNotification('Logged out successfully', 'success');
    }, 1000);
}

/**
 * Setup Parts Management functionality
 */
function setupPartsManagement() {
    setupPartsCategoryHandlers();
    setupPartsViewModes();
    setupPartsFormHandlers();
}

/**
 * Setup Parts Category handlers
 */
function setupPartsCategoryHandlers() {
    const categoryCards = document.querySelectorAll('.parts-category-card');
    const addButtons = document.querySelectorAll('.category-add-btn');
    const viewButtons = document.querySelectorAll('.category-view-btn');

    // Category card click handlers
    categoryCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking on buttons
            if (e.target.closest('.category-actions')) {
                return;
            }

            const category = this.getAttribute('data-category');
            showPartsCategory(category, 'view');
        });
    });

    // Add button handlers
    addButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const category = this.getAttribute('data-category');
            showPartsCategory(category, 'add');
        });
    });

    // View button handlers
    viewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const category = this.getAttribute('data-category');
            showPartsCategory(category, 'view');
        });
    });
}

/**
 * Show parts category content
 */
function showPartsCategory(category, mode) {
    const dynamicContent = document.getElementById('partsDynamicContent');
    if (!dynamicContent) return;

    const categoryNames = {
        'new-parts': 'New Parts',
        'salvage-parts': 'Salvage Parts',
        'core-parts': 'Core Parts',
        'exchange-parts': 'Exchange Parts'
    };

    const categoryName = categoryNames[category] || category;

    if (mode === 'add') {
        showPartsAddForm(category, categoryName);
    } else {
        showPartsViewList(category, categoryName);
    }
}

/**
 * Show add form for parts category
 */
function showPartsAddForm(category, categoryName) {
    const dynamicContent = document.getElementById('partsDynamicContent');

    const formHTML = `
        <div class="parts-form-container">
            <div class="parts-form-header">
                <h4>Add New ${categoryName}</h4>
                <p>Enter the details for the new ${categoryName.toLowerCase()}</p>
                <button class="btn btn-outline back-to-categories" onclick="backToPartsCategories()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 12H5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    Back to Categories
                </button>
            </div>

            <form class="parts-form" id="partsForm-${category}">
                ${getPartFormFields(category)}

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Save ${categoryName}</button>
                    <button type="button" class="btn btn-secondary" onclick="saveAsDraft('${category}')">Save as Draft</button>
                    <button type="button" class="btn btn-outline" onclick="clearPartsForm('${category}')">Clear Form</button>
                    <button type="button" class="btn btn-outline" onclick="previewPart('${category}')">Preview</button>
                </div>
            </form>
        </div>
    `;

    dynamicContent.innerHTML = formHTML;

    console.log('Form HTML generated for category:', category);
    console.log('Dynamic content updated');

    // Initialize searchable dropdowns for the new form
    initializeSearchableDropdowns(dynamicContent);

    // Setup quantity calculation for salvage parts
    if (category === 'salvage-parts') {
        setupSalvageQuantityCalculation(category);
    }

    setupPartsFormSubmission(category);

    // Initialize core parts tabs if this is a core parts form
    if (category === 'core-parts') {
        console.log('Core parts form detected, initializing tabs...');

        // Use multiple timeouts to ensure proper initialization
        setTimeout(() => {
            console.log('First initialization attempt...');
            initializeCorePartsTabs();
        }, 100);

        // Backup initialization in case the first one fails
        setTimeout(() => {
            const firstTab = document.getElementById('core-details');
            if (!firstTab || firstTab.style.display === 'none') {
                console.log('Backup initialization attempt...');
                initializeCorePartsTabs();
            }
        }, 500);
    }
}

/**
 * Show view list for parts category
 */
function showPartsViewList(category, categoryName) {
    const dynamicContent = document.getElementById('partsDynamicContent');

    const viewHTML = `
        <div class="parts-view-container">
            <div class="parts-view-header">
                <div class="view-title-section">
                    <h4>${categoryName} Records</h4>
                    <p>View and manage all ${categoryName.toLowerCase()} in the system</p>
                </div>
                <div class="view-actions">
                    <button class="btn btn-primary" onclick="showPartsCategory('${category}', 'add')">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2"/>
                            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        Add New
                    </button>
                    <button class="btn btn-outline back-to-categories" onclick="backToPartsCategories()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 12H5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Back to Categories
                    </button>
                </div>
            </div>

            <div class="view-controls">
                <div class="view-mode-selector">
                    <button class="view-mode-btn active" data-mode="list" onclick="setViewMode('${category}', 'list')">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2"/>
                            <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2"/>
                            <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2"/>
                            <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2"/>
                            <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2"/>
                            <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        List View
                    </button>
                    <button class="view-mode-btn" data-mode="card" onclick="setViewMode('${category}', 'card')">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                            <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                            <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                            <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        Card View
                    </button>
                    <button class="view-mode-btn" data-mode="compact" onclick="setViewMode('${category}', 'compact')">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="3" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2"/>
                            <line x1="3" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2"/>
                            <line x1="3" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        Compact View
                    </button>
                </div>

                <div class="search-filter-controls">
                    <input type="text" class="search-input" placeholder="Search ${categoryName.toLowerCase()}..." onkeyup="filterParts('${category}', this.value)">
                    <select class="filter-select" onchange="filterPartsByCategory('${category}', this.value)">
                        <option value="">All Categories</option>
                        <option value="ENGINE">Engine Parts</option>
                        <option value="HYDRAULIC">Hydraulic Parts</option>
                        <option value="ELECTRICAL">Electrical Parts</option>
                    </select>
                </div>
            </div>

            <div class="parts-data-container" id="partsDataContainer-${category}">
                ${getPartsListHTML(category, 'list')}
            </div>
        </div>
    `;

    dynamicContent.innerHTML = viewHTML;
}

/**
 * Back to parts categories
 */
function backToPartsCategories() {
    const dynamicContent = document.getElementById('partsDynamicContent');
    if (!dynamicContent) return;

    // Clear the dynamic content area - welcome section is now permanent above
    dynamicContent.innerHTML = '<!-- Content will be dynamically loaded here based on user selection -->';
}

/**
 * Setup parts view modes
 */
function setupPartsViewModes() {
    // This function will be called when needed
}

/**
 * Setup parts form handlers
 */
function setupPartsFormHandlers() {
    // This function will be called when needed
}

/**
 * Get salvage part form fields - only fields visible in desktop application
 */
function getSalvagePartFormFields(category) {
    return `
        <div class="form-sections">
            <!-- Top Section -->
            <div class="form-section">
                <div class="form-row">
                    <div class="form-group">
                        <label for="salvagePartPrefix-${category}">Salvage Part Prefix</label>
                        <select id="salvagePartPrefix-${category}" name="salvagePartPrefix" class="form-select">
                            <option value="">Select Prefix</option>
                            <option value="SAL" selected>SAL</option>
                            <option value="SCR">SCR</option>
                            <option value="REC">REC</option>
                        </select>
                    </div>
                    <div class="form-group radio-group">
                        <label>Active</label>
                        <div class="radio-options">
                            <label class="radio-label">
                                <input type="radio" name="activeStatus-${category}" value="yes" checked>
                                <span class="radio-custom"></span>
                                Yes
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="activeStatus-${category}" value="no">
                                <span class="radio-custom"></span>
                                No
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="newPartPrefix-${category}">New Part Prefix</label>
                        <select id="newPartPrefix-${category}" name="newPartPrefix" class="form-select">
                            <option value="">Select Prefix</option>
                            <option value="NEW" selected>NEW</option>
                            <option value="SAL">SAL</option>
                            <option value="COR">COR</option>
                            <option value="EXC">EXC</option>
                            <option value="REF">REF</option>
                            <option value="REM">REM</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="newPartNumber-${category}">New Part Number</label>
                        <input type="text" id="newPartNumber-${category}" name="newPartNumber" class="form-input" placeholder="Enter New Part Number" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group full-width">
                        <label for="newPartName-${category}">New Part Name</label>
                        <input type="text" id="newPartName-${category}" name="newPartName" class="form-input" placeholder="Enter New Part Name" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="hsnCode-${category}">HSN Code</label>
                        <input type="text" id="hsnCode-${category}" name="hsnCode" class="form-input" placeholder="Enter HSN Code">
                    </div>
                    <div class="form-group">
                        <label for="hsnSlNo-${category}">HSN Sl. No</label>
                        <input type="text" id="hsnSlNo-${category}" name="hsnSlNo" class="form-input" placeholder="HSN Serial Number">
                    </div>
                </div>
            </div>

            <!-- Quantity Section -->
            <div class="form-section">
                <h4 class="section-title">Quantity</h4>
                <div class="salvage-quantity-grid">
                    <!-- First Row -->
                    <div class="quantity-row">
                        <div class="quantity-item">
                            <label for="freeStock-${category}">Free Stock</label>
                            <input type="number" id="freeStock-${category}" name="freeStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="blockedQty-${category}">Blocked Qty</label>
                            <input type="number" id="blockedQty-${category}" name="blockedQty" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="allocatedStock-${category}">Allocated Stock</label>
                            <input type="number" id="allocatedStock-${category}" name="allocatedStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="pickedQty-${category}">Picked Qty</label>
                            <input type="number" id="pickedQty-${category}" name="pickedQty" class="form-input" value="0">
                        </div>
                    </div>
                    <!-- Second Row -->
                    <div class="quantity-row">
                        <div class="quantity-item">
                            <label for="deviationStock-${category}">Deviation Stock</label>
                            <input type="number" id="deviationStock-${category}" name="deviationStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="computerStock-${category}">Computer Stock</label>
                            <input type="number" id="computerStock-${category}" name="computerStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="git-${category}">GIT</label>
                            <input type="number" id="git-${category}" name="git" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="totalStock-${category}">Total Stock</label>
                            <input type="number" id="totalStock-${category}" name="totalStock" class="form-input" value="0" readonly>
                        </div>
                    </div>
                    <!-- Third Row -->
                    <div class="quantity-row">
                        <div class="quantity-item">
                            <label for="workshopStock-${category}">Workshop Stock</label>
                            <input type="number" id="workshopStock-${category}" name="workshopStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="vendorStock-${category}">Vendor Stock</label>
                            <input type="number" id="vendorStock-${category}" name="vendorStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item quantity-spacer">
                            <!-- Empty space to maintain grid alignment -->
                        </div>
                        <div class="quantity-item quantity-spacer">
                            <!-- Empty space to maintain grid alignment -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bin Location Details Section -->
            <div class="form-section">
                <h4 class="section-title">Bin Location Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="binLocationName-${category}">Name</label>
                        <select id="binLocationName-${category}" name="binLocationName" class="form-select">
                            <option value="">NO BIN</option>
                            <option value="A-01-4-A-04">A-01-4-A-04</option>
                            <option value="A-01-4-A-05">A-01-4-A-05</option>
                            <option value="SMALL BIN D13F">SMALL BIN D13F</option>
                            <option value="LARGE BIN A01">LARGE BIN A01</option>
                            <option value="MEDIUM BIN B05">MEDIUM BIN B05</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="binLocationCode-${category}">Code</label>
                        <select id="binLocationCode-${category}" name="binLocationCode" class="form-select">
                            <option value="">Select Code</option>
                            <option value="1">1</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="3293">3293</option>
                            <option value="1001">1001</option>
                            <option value="2005">2005</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Alternate Bin Location Details Section -->
            <div class="form-section">
                <h4 class="section-title">Alternate Bin Location Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="altBinLocationName-${category}">Name</label>
                        <select id="altBinLocationName-${category}" name="altBinLocationName" class="form-select">
                            <option value="">Select Alternate Bin Location</option>
                            <option value="A-01-4-A-05">A-01-4-A-05</option>
                            <option value="OVERFLOW BIN E20">OVERFLOW BIN E20</option>
                            <option value="BACKUP BIN F15">BACKUP BIN F15</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="altBinLocationCode-${category}">Code</label>
                        <select id="altBinLocationCode-${category}" name="altBinLocationCode" class="form-select">
                            <option value="">Select Code</option>
                            <option value="7">7</option>
                            <option value="20">20</option>
                            <option value="15">15</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Movement Details Section -->
            <div class="form-section">
                <h4 class="section-title">Movement Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="introductionDate-${category}">Introduction Date</label>
                        <input type="date" id="introductionDate-${category}" name="introductionDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="lastGrnDate-${category}">Last GRN Date</label>
                        <input type="date" id="lastGrnDate-${category}" name="lastGrnDate" class="form-input">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstDeliveryDate-${category}">First Delivery Date</label>
                        <input type="date" id="firstDeliveryDate-${category}" name="firstDeliveryDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="lastIssueDate-${category}">Last Issue Date</label>
                        <input type="date" id="lastIssueDate-${category}" name="lastIssueDate" class="form-input">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="lastDemandDate-${category}">Last Demand Date</label>
                        <input type="date" id="lastDemandDate-${category}" name="lastDemandDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="lastStockCheckDate-${category}">Last Stock Check Date</label>
                        <input type="date" id="lastStockCheckDate-${category}" name="lastStockCheckDate" class="form-input">
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Get core part form fields - comprehensive tabbed interface
 */
function getCorePartFormFields(category) {
    console.log('Generating core part form fields for category:', category);

    try {
        // Generate tab content first to ensure functions are called
        const coreDetailsContent = getCorePartDetailsTab(category);
        const bomDetailsContent = getBOMDetailsTab(category);
        const operationDetailsContent = getOperationDetailsTab(category);
        const grnIssueDetailsContent = getGRNIssueDetailsTab(category);

        console.log('Tab content generated successfully');
        console.log('Core details content length:', coreDetailsContent.length);
        console.log('BOM details content length:', bomDetailsContent.length);
        console.log('Operation details content length:', operationDetailsContent.length);
        console.log('GRN issue details content length:', grnIssueDetailsContent.length);

        const formHTML = `
            <div class="core-parts-master" data-category="${category}">
                <!-- Tab Navigation -->
                <div class="tab-navigation">
                    <button class="tab-btn active" onclick="switchCorePartsTab('core-details', this)" data-tab="core-details">Core Part Details</button>
                    <button class="tab-btn" onclick="switchCorePartsTab('bom-details', this)" data-tab="bom-details">BOM Details</button>
                    <button class="tab-btn" onclick="switchCorePartsTab('operation-details', this)" data-tab="operation-details">Operation Details</button>
                    <button class="tab-btn" onclick="switchCorePartsTab('grn-issue-details', this)" data-tab="grn-issue-details">GRN and Issue Details</button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Core Part Details Tab -->
                    <div id="core-details" class="tab-pane active" style="display: block;">
                        ${coreDetailsContent}
                    </div>

                    <!-- BOM Details Tab -->
                    <div id="bom-details" class="tab-pane" style="display: none;">
                        ${bomDetailsContent}
                    </div>

                    <!-- Operation Details Tab -->
                    <div id="operation-details" class="tab-pane" style="display: none;">
                        ${operationDetailsContent}
                    </div>

                    <!-- GRN and Issue Details Tab -->
                    <div id="grn-issue-details" class="tab-pane" style="display: none;">
                        ${grnIssueDetailsContent}
                    </div>
                </div>
            </div>
        `;

        console.log('Complete form HTML generated, length:', formHTML.length);
        return formHTML;
    } catch (error) {
        console.error('Error generating core part form fields:', error);
        return '<div class="error-message">Error generating form. Please try again.</div>';
    }
}

/**
 * Core Part Details Tab Content
 */
function getCorePartDetailsTab(category) {
    console.log('Generating core part details tab for category:', category);

    const content = `
        <div class="form-sections">
            <!-- Main Core Part Information -->
            <div class="form-section">
                <h4 class="section-title">Core Part Information</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="corePartPrefix-${category}">Core Part Prefix</label>
                        <select id="corePartPrefix-${category}" name="corePartPrefix" class="form-select" required>
                            <option value="COR" selected>COR</option>
                            <option value="NEW">NEW</option>
                            <option value="EXC">EXC</option>
                            <option value="REF">REF</option>
                            <option value="REM">REM</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="corePartNumber-${category}">Core Part Number</label>
                        <input type="text" id="corePartNumber-${category}" name="corePartNumber" class="form-input" placeholder="Enter Core Part Number" required>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="isSlNoRequired-${category}" name="isSlNoRequired"> Is Sl. No. Required?
                        </label>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="newPartPrefix-${category}">New Part Prefix</label>
                        <select id="newPartPrefix-${category}" name="newPartPrefix" class="form-select">
                            <option value="NEW" selected>NEW</option>
                            <option value="COR">COR</option>
                            <option value="EXC">EXC</option>
                            <option value="REF">REF</option>
                            <option value="REM">REM</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="newPartNumber-${category}">New Part Number</label>
                        <select id="newPartNumber-${category}" name="newPartNumber" class="form-select">
                            <option value="">Select New Part Number</option>
                            <option value="21479533">21479533</option>
                            <option value="21479534">21479534</option>
                            <option value="21479535">21479535</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Active</label>
                        <div class="radio-group">
                            <label><input type="radio" name="active-${category}" value="Yes" checked> Yes</label>
                            <label><input type="radio" name="active-${category}" value="No"> No</label>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group full-width">
                        <label for="newPartName-${category}">New Part Name</label>
                        <input type="text" id="newPartName-${category}" name="newPartName" class="form-input" placeholder="Enter New Part Name">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="hsnCode-${category}">HSN Code</label>
                        <select id="hsnCode-${category}" name="hsnCode" class="form-select">
                            <option value="">Select HSN Code</option>
                            <option value="84082020">84082020</option>
                            <option value="84082021">84082021</option>
                            <option value="84082022">84082022</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="hsnSlNo-${category}">HSN Sl No.</label>
                        <select id="hsnSlNo-${category}" name="hsnSlNo" class="form-select">
                            <option value="">Select HSN Sl No.</option>
                            <option value="57">57</option>
                            <option value="58">58</option>
                            <option value="59">59</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Salvage Part Details -->
            <div class="form-section">
                <h4 class="section-title">Salvage Part Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="salvagePartPrefix-${category}">Part Prefix</label>
                        <input type="text" id="salvagePartPrefix-${category}" name="salvagePartPrefix" class="form-input" placeholder="Enter Part Prefix">
                    </div>
                    <div class="form-group">
                        <label for="salvagePartNumber-${category}">Part Number</label>
                        <input type="text" id="salvagePartNumber-${category}" name="salvagePartNumber" class="form-input" placeholder="Enter Part Number">
                    </div>
                </div>
            </div>

            <!-- Business Area Details -->
            <div class="form-section">
                <h4 class="section-title">Business Area Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="businessAreaName-${category}">Name</label>
                        <select id="businessAreaName-${category}" name="businessAreaName" class="form-select">
                            <option value="">Select Business Area</option>
                            <option value="BUS" selected>BUS</option>
                            <option value="TRUCK">TRUCK</option>
                            <option value="MARINE">MARINE</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="businessAreaCode-${category}">Code</label>
                        <select id="businessAreaCode-${category}" name="businessAreaCode" class="form-select">
                            <option value="">Select Code</option>
                            <option value="1" selected>1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Default Bin Location Details -->
            <div class="form-section">
                <h4 class="section-title">Default Bin Location Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="defaultBinName-${category}">Name</label>
                        <select id="defaultBinName-${category}" name="defaultBinName" class="form-select">
                            <option value="">Select Bin Location</option>
                            <option value="NO BIN" selected>NO BIN</option>
                            <option value="BIN A">BIN A</option>
                            <option value="BIN B">BIN B</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="defaultBinCode-${category}">Code</label>
                        <select id="defaultBinCode-${category}" name="defaultBinCode" class="form-select">
                            <option value="">Select Code</option>
                            <option value="1" selected>1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Alternate Bin Location Details -->
            <div class="form-section">
                <h4 class="section-title">Alternate Bin Location Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="alternateBinName-${category}">Name</label>
                        <select id="alternateBinName-${category}" name="alternateBinName" class="form-select">
                            <option value="">Select Alternate Bin Location</option>
                            <option value="ALT BIN A">ALT BIN A</option>
                            <option value="ALT BIN B">ALT BIN B</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="alternateBinCode-${category}">Code</label>
                        <select id="alternateBinCode-${category}" name="alternateBinCode" class="form-select">
                            <option value="">Select Code</option>
                            <option value="1">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Core Part Quantity Details -->
            <div class="form-section">
                <h4 class="section-title">Core Part Quantity Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="gitFromDealer-${category}">GIT from Dealer</label>
                        <input type="number" id="gitFromDealer-${category}" name="gitFromDealer" class="form-input" value="0" min="0">
                    </div>
                    <div class="form-group">
                        <label for="computerStock-${category}">Computer Stock</label>
                        <input type="number" id="computerStock-${category}" name="computerStock" class="form-input" value="1" min="0">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="gitToInternalWorkshop-${category}">GIT to Internal Workshop</label>
                        <input type="number" id="gitToInternalWorkshop-${category}" name="gitToInternalWorkshop" class="form-input" value="0" min="0">
                    </div>
                    <div class="form-group">
                        <label for="averageLeadTime-${category}">Average Lead Time in Days</label>
                        <input type="number" id="averageLeadTime-${category}" name="averageLeadTime" class="form-input" value="6" min="0">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group full-width">
                        <label for="coreRate-${category}">Core Rate</label>
                        <input type="number" id="coreRate-${category}" name="coreRate" class="form-input" value="10912.00" step="0.01" min="0">
                    </div>
                </div>
            </div>

            <!-- Remarks -->
            <div class="form-section">
                <h4 class="section-title">Remarks</h4>
                <div class="form-row">
                    <div class="form-group full-width">
                        <textarea id="remarks-${category}" name="remarks" class="form-textarea" rows="4" placeholder="Enter remarks..."></textarea>
                    </div>
                </div>
            </div>
        </div>
    `;

    console.log('Core part details tab content generated, length:', content.length);
    return content;
}

/**
 * BOM Details Tab Content
 */
function getBOMDetailsTab(category) {
    return `
        <div class="bom-details-tab">
            <!-- Version Details Section -->
            <div class="form-section">
                <h4 class="section-title">Version Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="createNew-${category}" name="createNew"> Create New
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="versionNumber-${category}">Number</label>
                        <select id="versionNumber-${category}" name="versionNumber" class="form-select">
                            <option value="1" selected>1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="versionDescription-${category}">Description</label>
                        <input type="text" id="versionDescription-${category}" name="versionDescription" class="form-input" placeholder="Enter version description">
                    </div>
                </div>
            </div>

            <!-- Search Section -->
            <div class="form-section">
                <h4 class="section-title">Search By</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="searchBy-${category}">Search By</label>
                        <select id="searchBy-${category}" name="searchBy" class="form-select">
                            <option value="partNumber" selected>Part Number</option>
                            <option value="partName">Part Name</option>
                            <option value="description">Description</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="searchPartNumber-${category}">Part Number</label>
                        <input type="text" id="searchPartNumber-${category}" name="searchPartNumber" class="form-input" placeholder="Enter part number to search">
                    </div>
                    <div class="form-group">
                        <label for="searchPartName-${category}">Part Name</label>
                        <input type="text" id="searchPartName-${category}" name="searchPartName" class="form-input" placeholder="Enter part name to search">
                    </div>
                </div>
            </div>

            <!-- BOM Table -->
            <div class="form-section">
                <div class="table-container">
                    <table class="data-table bom-table">
                        <thead>
                            <tr>
                                <th width="5%"></th>
                                <th width="10%">Prefix</th>
                                <th width="15%">Part Number</th>
                                <th width="25%">Part Description</th>
                                <th width="8%">Quantity</th>
                                <th width="12%">Is Mandatory Replacement?</th>
                                <th width="12%">% of Parts Consumption Estimated</th>
                                <th width="13%">% of Parts Consumption Actual</th>
                            </tr>
                        </thead>
                        <tbody id="bomTableBody-${category}">
                            ${getBOMTableRows()}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Add BOM Item Form -->
            <div class="form-section">
                <h4 class="section-title">Add BOM Item</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="bomPrefix-${category}">Prefix</label>
                        <select id="bomPrefix-${category}" name="bomPrefix" class="form-select">
                            <option value="NEW" selected>NEW</option>
                            <option value="COR">COR</option>
                            <option value="EXC">EXC</option>
                            <option value="REF">REF</option>
                            <option value="REM">REM</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bomPartNumber-${category}">Part Number</label>
                        <input type="text" id="bomPartNumber-${category}" name="bomPartNumber" class="form-input" placeholder="Enter Part Number">
                    </div>
                    <div class="form-group">
                        <label for="bomPartDescription-${category}">Part Description</label>
                        <input type="text" id="bomPartDescription-${category}" name="bomPartDescription" class="form-input" placeholder="Enter Part Description">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="bomQuantity-${category}">Quantity</label>
                        <input type="number" id="bomQuantity-${category}" name="bomQuantity" class="form-input" min="1" value="1">
                    </div>
                    <div class="form-group">
                        <label for="bomIsMandatory-${category}">Is Mandatory?</label>
                        <select id="bomIsMandatory-${category}" name="bomIsMandatory" class="form-select">
                            <option value="No" selected>No</option>
                            <option value="Yes">Yes</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bomConsumptionEstimated-${category}">% Consumption Estimated</label>
                        <input type="number" id="bomConsumptionEstimated-${category}" name="bomConsumptionEstimated" class="form-input" step="0.01" min="0" max="100" value="100.00">
                    </div>
                    <div class="form-group">
                        <label for="bomConsumptionActual-${category}">% Consumption Actual</label>
                        <input type="number" id="bomConsumptionActual-${category}" name="bomConsumptionActual" class="form-input" step="0.01" min="0" max="100" value="0.00">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <button type="button" class="btn btn-primary" onclick="addBOMItem('${category}')">Add to BOM</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Operation Details Tab Content
 */
function getOperationDetailsTab(category) {
    return `
        <div class="operation-details-tab">
            <div class="form-section">
                <div class="table-container">
                    <table class="data-table operation-table">
                        <thead>
                            <tr>
                                <th width="15%">Operation Code</th>
                                <th width="30%">Operation Name</th>
                                <th width="12%">Standard Time</th>
                                <th width="10%">Quantity</th>
                                <th width="12%">Is Mandatory?</th>
                                <th width="10%">% of Usage Estimated</th>
                                <th width="11%">% of Usage Actual</th>
                            </tr>
                        </thead>
                        <tbody id="operationTableBody-${category}">
                            <!-- Operation data will be populated here -->
                            <tr>
                                <td colspan="7" class="text-center text-muted">No operation details available</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Add Operation Form -->
            <div class="form-section">
                <h4 class="section-title">Add Operation</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="operationCode-${category}">Operation Code</label>
                        <input type="text" id="operationCode-${category}" name="operationCode" class="form-input" placeholder="Enter Operation Code">
                    </div>
                    <div class="form-group">
                        <label for="operationName-${category}">Operation Name</label>
                        <input type="text" id="operationName-${category}" name="operationName" class="form-input" placeholder="Enter Operation Name">
                    </div>
                    <div class="form-group">
                        <label for="standardTime-${category}">Standard Time</label>
                        <input type="number" id="standardTime-${category}" name="standardTime" class="form-input" step="0.01" min="0" placeholder="0.00">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="operationQuantity-${category}">Quantity</label>
                        <input type="number" id="operationQuantity-${category}" name="operationQuantity" class="form-input" min="1" value="1">
                    </div>
                    <div class="form-group">
                        <label for="operationIsMandatory-${category}">Is Mandatory?</label>
                        <select id="operationIsMandatory-${category}" name="operationIsMandatory" class="form-select">
                            <option value="No" selected>No</option>
                            <option value="Yes">Yes</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="usageEstimated-${category}">% of Usage Estimated</label>
                        <input type="number" id="usageEstimated-${category}" name="usageEstimated" class="form-input" step="0.01" min="0" max="100" value="100.00">
                    </div>
                    <div class="form-group">
                        <label for="usageActual-${category}">% of Usage Actual</label>
                        <input type="number" id="usageActual-${category}" name="usageActual" class="form-input" step="0.01" min="0" max="100" value="0.00">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <button type="button" class="btn btn-primary" onclick="addOperation('${category}')">Add Operation</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * GRN and Issue Details Tab Content
 */
function getGRNIssueDetailsTab(category) {
    return `
        <div class="grn-issue-details-tab">
            <div class="form-section">
                <div class="table-container">
                    <table class="data-table grn-issue-table">
                        <thead>
                            <tr>
                                <th width="5%"></th>
                                <th width="12%">Core Receipt GRN No.</th>
                                <th width="12%">Core Receipt GRN Date</th>
                                <th width="10%">Serial No.</th>
                                <th width="10%">Core Cost</th>
                                <th width="10%">Issue Core No.</th>
                                <th width="12%">Issue Core Date</th>
                                <th width="10%">Stock Adj. No.</th>
                                <th width="12%">Stock Adj. Date</th>
                            </tr>
                        </thead>
                        <tbody id="grnIssueTableBody-${category}">
                            ${getGRNIssueTableRows()}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Add GRN/Issue Record Form -->
            <div class="form-section">
                <h4 class="section-title">Add GRN/Issue Record</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="coreReceiptGRNNo-${category}">Core Receipt GRN No.</label>
                        <input type="text" id="coreReceiptGRNNo-${category}" name="coreReceiptGRNNo" class="form-input" placeholder="Enter GRN Number">
                    </div>
                    <div class="form-group">
                        <label for="coreReceiptGRNDate-${category}">Core Receipt GRN Date</label>
                        <input type="date" id="coreReceiptGRNDate-${category}" name="coreReceiptGRNDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="serialNo-${category}">Serial No.</label>
                        <input type="text" id="serialNo-${category}" name="serialNo" class="form-input" placeholder="Enter Serial Number">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="coreCost-${category}">Core Cost</label>
                        <input type="number" id="coreCost-${category}" name="coreCost" class="form-input" step="0.01" min="0" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label for="issueCoreNo-${category}">Issue Core No.</label>
                        <input type="text" id="issueCoreNo-${category}" name="issueCoreNo" class="form-input" placeholder="Enter Issue Core Number">
                    </div>
                    <div class="form-group">
                        <label for="issueCoreDate-${category}">Issue Core Date</label>
                        <input type="date" id="issueCoreDate-${category}" name="issueCoreDate" class="form-input">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="stockAdjNo-${category}">Stock Adj. No.</label>
                        <input type="text" id="stockAdjNo-${category}" name="stockAdjNo" class="form-input" placeholder="Enter Stock Adjustment Number">
                    </div>
                    <div class="form-group">
                        <label for="stockAdjDate-${category}">Stock Adj. Date</label>
                        <input type="date" id="stockAdjDate-${category}" name="stockAdjDate" class="form-input">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <button type="button" class="btn btn-primary" onclick="addGRNIssueRecord('${category}')">Add Record</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Generate BOM Table Rows with sample data
 */
function getBOMTableRows() {
    const bomData = [
        { prefix: 'NEW', partNumber: '11700269', description: 'SCREW', quantity: 1, mandatory: 'No', estimated: '100.00', actual: '0.00' },
        { prefix: 'NEW', partNumber: '11700279', description: 'PLUG', quantity: 2, mandatory: 'No', estimated: '10.00', actual: '0.00' },
        { prefix: 'NEW', partNumber: '11700326', description: 'FLANGE SCREW', quantity: 3, mandatory: 'No', estimated: '100.00', actual: '100.00' },
        { prefix: 'NEW', partNumber: '11700373', description: 'HEXAGON NUT', quantity: 4, mandatory: 'No', estimated: '10.00', actual: '0.00' },
        { prefix: 'NEW', partNumber: '11398', description: 'GASKET, 18*24', quantity: 5, mandatory: 'Yes', estimated: '100.00', actual: '100.00' },
        { prefix: 'NEW', partNumber: '20405504', description: 'HEXAGON SCREW', quantity: 6, mandatory: 'No', estimated: '100.00', actual: '0.00' },
        { prefix: 'NEW', partNumber: '20405562', description: 'HOLLOW SCREW', quantity: 7, mandatory: 'No', estimated: '10.00', actual: '40.00' },
        { prefix: 'NEW', partNumber: '20405568', description: 'HOSE CLAMP', quantity: 8, mandatory: 'No', estimated: '100.00', actual: '100.00' },
        { prefix: 'NEW', partNumber: '20405599', description: 'O-RING', quantity: 9, mandatory: 'Yes', estimated: '100.00', actual: '100.00' },
        { prefix: 'NEW', partNumber: '20405603', description: 'SPRING', quantity: 1, mandatory: 'Yes', estimated: '100.00', actual: '100.00' }
    ];

    return bomData.map((item, index) => `
        <tr>
            <td><input type="checkbox" class="row-checkbox"></td>
            <td>${item.prefix}</td>
            <td>${item.partNumber}</td>
            <td>${item.description}</td>
            <td>${item.quantity}</td>
            <td><span class="badge ${item.mandatory === 'Yes' ? 'badge-danger' : 'badge-secondary'}">${item.mandatory}</span></td>
            <td>${item.estimated}</td>
            <td>${item.actual}</td>
        </tr>
    `).join('');
}

/**
 * Generate GRN Issue Table Rows with sample data
 */
function getGRNIssueTableRows() {
    const grnData = [
        {
            grnNo: '10',
            grnDate: '13-Apr-2015',
            serialNo: '11068822',
            coreCost: '10912.00',
            issueCoreNo: '',
            issueCoreDate: '',
            stockAdjNo: '',
            stockAdjDate: ''
        },
        {
            grnNo: '9',
            grnDate: '13-Apr-2015',
            serialNo: '11323173',
            coreCost: '10912.00',
            issueCoreNo: '3',
            issueCoreDate: '14-Apr-2015',
            stockAdjNo: '',
            stockAdjDate: ''
        }
    ];

    return grnData.map((item, index) => `
        <tr>
            <td><input type="checkbox" class="row-checkbox"></td>
            <td>${item.grnNo}</td>
            <td>${item.grnDate}</td>
            <td>${item.serialNo}</td>
            <td>${item.coreCost}</td>
            <td>${item.issueCoreNo}</td>
            <td>${item.issueCoreDate}</td>
            <td>${item.stockAdjNo}</td>
            <td>${item.stockAdjDate}</td>
        </tr>
    `).join('');
}

/**
 * Switch between Core Parts Master tabs
 */
function switchCorePartsTab(tabId, buttonElement) {
    console.log('Switching to tab:', tabId); // Debug log

    try {
        // Hide all tab panes using inline styles for reliability
        const allTabIds = ['core-details', 'bom-details', 'operation-details', 'grn-issue-details'];
        allTabIds.forEach(id => {
            const tab = document.getElementById(id);
            if (tab) {
                tab.style.display = 'none';
                tab.classList.remove('active');
                console.log('Hidden tab:', id);
            } else {
                console.warn('Tab element not found:', id);
            }
        });

        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.core-parts-master .tab-btn');
        tabButtons.forEach(btn => {
            btn.classList.remove('active');
        });

        // Show selected tab pane
        const selectedTab = document.getElementById(tabId);
        console.log('Selected tab element:', selectedTab); // Debug log
        if (selectedTab) {
            selectedTab.style.display = 'block';
            selectedTab.classList.add('active');

            // Force a reflow to ensure content is rendered
            selectedTab.offsetHeight;

            console.log('Tab switched successfully to:', tabId);

            // Trigger any tab-specific initialization
            initializeTabContent(tabId);
        } else {
            console.error('Tab not found:', tabId);
            // Try to regenerate the form if tabs are missing
            console.log('Attempting to regenerate core parts form...');
            const dynamicContent = document.getElementById('partsDynamicContent');
            if (dynamicContent && dynamicContent.innerHTML.includes('core-parts-master')) {
                // Force re-render of the form
                const category = 'core-parts';
                const formHTML = getCorePartFormFields(category);
                if (formHTML && !formHTML.includes('error-message')) {
                    dynamicContent.innerHTML = formHTML;
                    setTimeout(() => {
                        initializeCorePartsTabs();
                        // Try switching to the requested tab again
                        setTimeout(() => {
                            switchCorePartsTab(tabId, buttonElement);
                        }, 200);
                    }, 100);
                }
            }
        }

        // Add active class to clicked button
        if (buttonElement) {
            buttonElement.classList.add('active');
        }
    } catch (error) {
        console.error('Error switching core parts tab:', error);
        showNotification('Error switching tabs. Please try again.', 'error');
    }
}

/**
 * Add BOM Item to the table
 */
function addBOMItem(category) {
    const prefix = document.getElementById(`bomPrefix-${category}`).value;
    const partNumber = document.getElementById(`bomPartNumber-${category}`).value;
    const description = document.getElementById(`bomPartDescription-${category}`).value;
    const quantity = document.getElementById(`bomQuantity-${category}`).value;
    const mandatory = document.getElementById(`bomIsMandatory-${category}`).value;
    const estimated = document.getElementById(`bomConsumptionEstimated-${category}`).value;
    const actual = document.getElementById(`bomConsumptionActual-${category}`).value;

    if (!partNumber || !description) {
        showNotification('Please fill in Part Number and Description', 'error');
        return;
    }

    const tableBody = document.getElementById(`bomTableBody-${category}`);
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td><input type="checkbox" class="row-checkbox"></td>
        <td>${prefix}</td>
        <td>${partNumber}</td>
        <td>${description}</td>
        <td>${quantity}</td>
        <td><span class="badge ${mandatory === 'Yes' ? 'badge-danger' : 'badge-secondary'}">${mandatory}</span></td>
        <td>${estimated}</td>
        <td>${actual}</td>
    `;

    tableBody.appendChild(newRow);

    // Clear form
    document.getElementById(`bomPartNumber-${category}`).value = '';
    document.getElementById(`bomPartDescription-${category}`).value = '';
    document.getElementById(`bomQuantity-${category}`).value = '1';
    document.getElementById(`bomIsMandatory-${category}`).value = 'No';
    document.getElementById(`bomConsumptionEstimated-${category}`).value = '100.00';
    document.getElementById(`bomConsumptionActual-${category}`).value = '0.00';

    showNotification('BOM item added successfully', 'success');
}

/**
 * Add Operation to the table
 */
function addOperation(category) {
    const operationCode = document.getElementById(`operationCode-${category}`).value;
    const operationName = document.getElementById(`operationName-${category}`).value;
    const standardTime = document.getElementById(`standardTime-${category}`).value;
    const quantity = document.getElementById(`operationQuantity-${category}`).value;
    const mandatory = document.getElementById(`operationIsMandatory-${category}`).value;
    const estimated = document.getElementById(`usageEstimated-${category}`).value;
    const actual = document.getElementById(`usageActual-${category}`).value;

    if (!operationCode || !operationName) {
        showNotification('Please fill in Operation Code and Name', 'error');
        return;
    }

    const tableBody = document.getElementById(`operationTableBody-${category}`);

    // Remove "no data" row if it exists
    const noDataRow = tableBody.querySelector('td[colspan="7"]');
    if (noDataRow) {
        noDataRow.parentElement.remove();
    }

    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>${operationCode}</td>
        <td>${operationName}</td>
        <td>${standardTime}</td>
        <td>${quantity}</td>
        <td><span class="badge ${mandatory === 'Yes' ? 'badge-danger' : 'badge-secondary'}">${mandatory}</span></td>
        <td>${estimated}</td>
        <td>${actual}</td>
    `;

    tableBody.appendChild(newRow);

    // Clear form
    document.getElementById(`operationCode-${category}`).value = '';
    document.getElementById(`operationName-${category}`).value = '';
    document.getElementById(`standardTime-${category}`).value = '';
    document.getElementById(`operationQuantity-${category}`).value = '1';
    document.getElementById(`operationIsMandatory-${category}`).value = 'No';
    document.getElementById(`usageEstimated-${category}`).value = '100.00';
    document.getElementById(`usageActual-${category}`).value = '0.00';

    showNotification('Operation added successfully', 'success');
}

/**
 * Add GRN/Issue Record to the table
 */
function addGRNIssueRecord(category) {
    const grnNo = document.getElementById(`coreReceiptGRNNo-${category}`).value;
    const grnDate = document.getElementById(`coreReceiptGRNDate-${category}`).value;
    const serialNo = document.getElementById(`serialNo-${category}`).value;
    const coreCost = document.getElementById(`coreCost-${category}`).value;
    const issueCoreNo = document.getElementById(`issueCoreNo-${category}`).value;
    const issueCoreDate = document.getElementById(`issueCoreDate-${category}`).value;
    const stockAdjNo = document.getElementById(`stockAdjNo-${category}`).value;
    const stockAdjDate = document.getElementById(`stockAdjDate-${category}`).value;

    if (!grnNo || !grnDate || !serialNo) {
        showNotification('Please fill in GRN No., GRN Date, and Serial No.', 'error');
        return;
    }

    const tableBody = document.getElementById(`grnIssueTableBody-${category}`);
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td><input type="checkbox" class="row-checkbox"></td>
        <td>${grnNo}</td>
        <td>${grnDate}</td>
        <td>${serialNo}</td>
        <td>${coreCost}</td>
        <td>${issueCoreNo}</td>
        <td>${issueCoreDate}</td>
        <td>${stockAdjNo}</td>
        <td>${stockAdjDate}</td>
    `;

    tableBody.appendChild(newRow);

    // Clear form
    document.getElementById(`coreReceiptGRNNo-${category}`).value = '';
    document.getElementById(`coreReceiptGRNDate-${category}`).value = '';
    document.getElementById(`serialNo-${category}`).value = '';
    document.getElementById(`coreCost-${category}`).value = '';
    document.getElementById(`issueCoreNo-${category}`).value = '';
    document.getElementById(`issueCoreDate-${category}`).value = '';
    document.getElementById(`stockAdjNo-${category}`).value = '';
    document.getElementById(`stockAdjDate-${category}`).value = '';

    showNotification('GRN/Issue record added successfully', 'success');
}

/**
 * Setup BOM table interactions
 */
function setupBOMTableInteractions() {
    console.log('Setting up BOM table interactions');
    // Add any BOM-specific functionality here
    const bomTable = document.querySelector('.bom-table');
    if (bomTable) {
        // Setup row selection
        const checkboxes = bomTable.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const row = this.closest('tr');
                if (this.checked) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
            });
        });
    }
}

/**
 * Setup Operation table interactions
 */
function setupOperationTableInteractions() {
    console.log('Setting up Operation table interactions');
    // Add any operation-specific functionality here
}

/**
 * Setup GRN Issue table interactions
 */
function setupGRNIssueTableInteractions() {
    console.log('Setting up GRN Issue table interactions');
    // Add any GRN issue-specific functionality here
    const grnTable = document.querySelector('.grn-issue-table');
    if (grnTable) {
        // Setup row selection
        const checkboxes = grnTable.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const row = this.closest('tr');
                if (this.checked) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
            });
        });
    }
}

/**
 * Initialize tab-specific content
 */
function initializeTabContent(tabId) {
    console.log('Initializing content for tab:', tabId);

    try {
        switch(tabId) {
            case 'core-details':
                // Initialize any specific functionality for core details tab
                const coreDetailsTab = document.getElementById(tabId);
                if (coreDetailsTab) {
                    initializeSearchableDropdowns(coreDetailsTab);
                }
                break;
            case 'bom-details':
                // Initialize BOM table functionality
                setupBOMTableInteractions();
                break;
            case 'operation-details':
                // Initialize operation table functionality
                setupOperationTableInteractions();
                break;
            case 'grn-issue-details':
                // Initialize GRN issue table functionality
                setupGRNIssueTableInteractions();
                break;
            default:
                console.log('No specific initialization needed for tab:', tabId);
        }
    } catch (error) {
        console.error('Error initializing tab content:', error);
    }
}

/**
 * Initialize Core Parts Tabs - ensure proper display
 */
function initializeCorePartsTabs() {
    console.log('Initializing core parts tabs...');

    // Wait a bit for DOM to be fully rendered
    setTimeout(() => {
        try {
            // Check if core parts master container exists
            const corePartsMaster = document.querySelector('.core-parts-master');
            if (!corePartsMaster) {
                console.error('Core parts master container not found');
                return;
            }

            // Hide all tabs first using specific IDs
            const allTabIds = ['core-details', 'bom-details', 'operation-details', 'grn-issue-details'];
            let tabsFound = 0;

            allTabIds.forEach(id => {
                const tab = document.getElementById(id);
                if (tab) {
                    tab.style.display = 'none';
                    tab.classList.remove('active');
                    tabsFound++;
                    console.log('Hidden tab:', id);
                } else {
                    console.warn('Tab not found during initialization:', id);
                }
            });

            if (tabsFound === 0) {
                console.error('No tabs found during initialization');
                return;
            }

            // Show the first tab (core-details)
            const firstTab = document.getElementById('core-details');
            if (firstTab) {
                firstTab.style.display = 'block';
                firstTab.classList.add('active');

                // Force reflow
                firstTab.offsetHeight;

                console.log('First tab (core-details) initialized and shown');

                // Initialize content for the first tab
                initializeTabContent('core-details');
            } else {
                console.error('First tab (core-details) not found during initialization');
            }

            // Ensure first tab button is active
            const firstTabButton = document.querySelector('.core-parts-master .tab-btn');
            if (firstTabButton) {
                // Remove active from all buttons first
                document.querySelectorAll('.core-parts-master .tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                firstTabButton.classList.add('active');
                console.log('First tab button activated');
            } else {
                console.error('First tab button not found');
            }

            console.log('Core parts tabs initialization complete');
        } catch (error) {
            console.error('Error during core parts tabs initialization:', error);
        }
    }, 200);
}

/**
 * Get part form fields based on category
 */
function getPartFormFields(category) {
    // For salvage parts, show only the fields visible in desktop application
    if (category === 'salvage-parts') {
        return getSalvagePartFormFields(category);
    }

    // For core parts, show simplified essential fields only
    if (category === 'core-parts') {
        return getCorePartFormFields(category);
    }

    // For other categories, show the original comprehensive form
    const formFields = `
        <div class="form-sections">
            <!-- New Part Details Section -->
            <div class="form-section">
                <h4 class="section-title">New Part Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="partCode-${category}">Part Code</label>
                        <input type="text" id="partCode-${category}" name="partCode" class="form-input" placeholder="Enter Part Code" required>
                    </div>
                    <div class="form-group radio-group">
                        <label>Active</label>
                        <div class="radio-options">
                            <label class="radio-label">
                                <input type="radio" name="activeStatus-${category}" value="yes" checked>
                                <span class="radio-custom"></span>
                                Yes
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="activeStatus-${category}" value="no">
                                <span class="radio-custom"></span>
                                No
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="partPrefix-${category}">Part Prefix</label>
                        <select id="partPrefix-${category}" name="partPrefix" class="form-select">
                            <option value="">Select Prefix</option>
                            <option value="NEW" ${category === 'new-parts' ? 'selected' : ''}>NEW</option>
                            <option value="SAL" ${category === 'salvage-parts' ? 'selected' : ''}>SAL</option>
                            <option value="COR" ${category === 'core-parts' ? 'selected' : ''}>COR</option>
                            <option value="EXC" ${category === 'exchange-parts' ? 'selected' : ''}>EXC</option>
                            <option value="REF">REF</option>
                            <option value="REM">REM</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="salvagePartPrefix-${category}">Salvage Part Prefix</label>
                        <select id="salvagePartPrefix-${category}" name="salvagePartPrefix" class="form-select">
                            <option value="">Select Prefix</option>
                            <option value="SAL" selected>SAL</option>
                            <option value="SCR">SCR</option>
                            <option value="REC">REC</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="partNumber-${category}">Part Number</label>
                        <input type="text" id="partNumber-${category}" name="partNumber" class="form-input" placeholder="Enter Part Number" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group full-width">
                        <label for="partName-${category}">Part Name</label>
                        <input type="text" id="partName-${category}" name="partName" class="form-input" placeholder="Enter Part Name" required>
                    </div>
                </div>
            </div>



            <!-- Quantity Section - Matching Desktop Layout -->
            <div class="form-section">
                <h4 class="section-title">Quantity</h4>
                <div class="salvage-quantity-grid">
                    <!-- First Row -->
                    <div class="quantity-row">
                        <div class="quantity-item">
                            <label for="freeStock-${category}">Free Stock</label>
                            <input type="number" id="freeStock-${category}" name="freeStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="blockedQty-${category}">Blocked Qty</label>
                            <input type="number" id="blockedQty-${category}" name="blockedQty" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="allocatedStock-${category}">Allocated Stock</label>
                            <input type="number" id="allocatedStock-${category}" name="allocatedStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="pickedQty-${category}">Picked Qty</label>
                            <input type="number" id="pickedQty-${category}" name="pickedQty" class="form-input" value="0">
                        </div>
                    </div>
                    <!-- Second Row -->
                    <div class="quantity-row">
                        <div class="quantity-item">
                            <label for="deviationStock-${category}">Deviation Stock</label>
                            <input type="number" id="deviationStock-${category}" name="deviationStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="computerStock-${category}">Computer Stock</label>
                            <input type="number" id="computerStock-${category}" name="computerStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="git-${category}">GIT</label>
                            <input type="number" id="git-${category}" name="git" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="totalStock-${category}">Total Stock</label>
                            <input type="number" id="totalStock-${category}" name="totalStock" class="form-input" value="0" readonly>
                        </div>
                    </div>
                    <!-- Third Row -->
                    <div class="quantity-row">
                        <div class="quantity-item">
                            <label for="workshopStock-${category}">Workshop Stock</label>
                            <input type="number" id="workshopStock-${category}" name="workshopStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item">
                            <label for="vendorStock-${category}">Vendor Stock</label>
                            <input type="number" id="vendorStock-${category}" name="vendorStock" class="form-input" value="0">
                        </div>
                        <div class="quantity-item quantity-spacer">
                            <!-- Empty space to maintain grid alignment -->
                        </div>
                        <div class="quantity-item quantity-spacer">
                            <!-- Empty space to maintain grid alignment -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bin Location Details Section -->
            <div class="form-section">
                <h4 class="section-title">Bin Location Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="binLocationName-${category}">Name</label>
                        <select id="binLocationName-${category}" name="binLocationName" class="form-select">
                            <option value="">NO BIN</option>
                            <option value="A-01-4-A-04">A-01-4-A-04</option>
                            <option value="A-01-4-A-05">A-01-4-A-05</option>
                            <option value="SMALL BIN D13F">SMALL BIN D13F</option>
                            <option value="LARGE BIN A01">LARGE BIN A01</option>
                            <option value="MEDIUM BIN B05">MEDIUM BIN B05</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="binLocationCode-${category}">Code</label>
                        <select id="binLocationCode-${category}" name="binLocationCode" class="form-select">
                            <option value="">Select Code</option>
                            <option value="1">1</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="3293">3293</option>
                            <option value="1001">1001</option>
                            <option value="2005">2005</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Alternate Bin Location Details Section -->
            <div class="form-section">
                <h4 class="section-title">Alternate Bin Location Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="altBinLocationName-${category}">Name</label>
                        <select id="altBinLocationName-${category}" name="altBinLocationName" class="form-select">
                            <option value="">Select Alternate Bin Location</option>
                            <option value="A-01-4-A-05">A-01-4-A-05</option>
                            <option value="OVERFLOW BIN E20">OVERFLOW BIN E20</option>
                            <option value="BACKUP BIN F15">BACKUP BIN F15</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="altBinLocationCode-${category}">Code</label>
                        <select id="altBinLocationCode-${category}" name="altBinLocationCode" class="form-select">
                            <option value="">Select Code</option>
                            <option value="7">7</option>
                            <option value="20">20</option>
                            <option value="15">15</option>
                        </select>
                    </div>
                </div>
            </div>



            <!-- Movement Details Section -->
            <div class="form-section">
                <h4 class="section-title">Movement Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="introductionDate-${category}">Introduction Date</label>
                        <input type="date" id="introductionDate-${category}" name="introductionDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="lastGrnDate-${category}">Last GRN Date</label>
                        <input type="date" id="lastGrnDate-${category}" name="lastGrnDate" class="form-input">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstDeliveryDate-${category}">First Delivery Date</label>
                        <input type="date" id="firstDeliveryDate-${category}" name="firstDeliveryDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="lastIssueDate-${category}">Last Issue Date</label>
                        <input type="date" id="lastIssueDate-${category}" name="lastIssueDate" class="form-input">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="lastDemandDate-${category}">Last Demand Date</label>
                        <input type="date" id="lastDemandDate-${category}" name="lastDemandDate" class="form-input">
                    </div>
                    <div class="form-group">
                        <label for="lastStockCheckDate-${category}">Last Stock Check Date</label>
                        <input type="date" id="lastStockCheckDate-${category}" name="lastStockCheckDate" class="form-input">
                    </div>
                </div>
            </div>


        </div>
    `;

    return formFields;
}

/**
 * Get parts list HTML
 */
function getPartsListHTML(category, viewMode) {
    // Determine part type based on category
    const getPartType = (category) => {
        const categoryMap = {
            'new-parts': 'new',
            'salvage-parts': 'salvage',
            'core-parts': 'core',
            'exchange-parts': 'exchange'
        };
        return categoryMap[category] || 'new';
    };

    const partType = getPartType(category);

    // Sample data based on category
    let sampleParts;

    if (category === 'core-parts') {
        sampleParts = [
            {
                id: 1,
                prefix: 'COR',
                partNumber: '11700269',
                partDescription: 'SCREW',
                quantity: 1,
                isMandatory: 'No',
                partsConsumptionEstimated: 100.00,
                partsConsumptionActual: 0.00,
                status: 'Active'
            },
            {
                id: 2,
                prefix: 'COR',
                partNumber: '11700279',
                partDescription: 'PLUG',
                quantity: 2,
                isMandatory: 'No',
                partsConsumptionEstimated: 10.00,
                partsConsumptionActual: 0.00,
                status: 'Active'
            },
            {
                id: 3,
                prefix: 'COR',
                partNumber: '11700326',
                partDescription: 'FLANGE SCREW',
                quantity: 3,
                isMandatory: 'No',
                partsConsumptionEstimated: 100.00,
                partsConsumptionActual: 100.00,
                status: 'Active'
            },
            {
                id: 4,
                prefix: 'COR',
                partNumber: '11700373',
                partDescription: 'HEXAGON NUT',
                quantity: 4,
                isMandatory: 'No',
                partsConsumptionEstimated: 10.00,
                partsConsumptionActual: 0.00,
                status: 'Active'
            },
            {
                id: 5,
                prefix: 'COR',
                partNumber: '11398',
                partDescription: 'GASKET, 18*24',
                quantity: 5,
                isMandatory: 'Yes',
                partsConsumptionEstimated: 100.00,
                partsConsumptionActual: 100.00,
                status: 'Active'
            }
        ];
    } else if (category === 'salvage-parts') {
        sampleParts = [
        {
            id: 1,
            salvagePartPrefix: 'SAL',
            newPartPrefix: 'NEW',
            newPartNumber: '183272',
            newPartName: 'NEEDLE ROLLER BUSHING',
            hsnCode: '84824000',
            hsnSlNo: '30',
            freeStock: 0,
            blockedQty: 0,
            allocatedStock: 0,
            pickedQty: 0,
            deviationStock: 0,
            computerStock: 0,
            git: 0,
            totalStock: 0,
            workshopStock: 0,
            vendorStock: 0,
            binLocation: 'NO BIN',
            binCode: '1',
            status: 'Active',
            active: 'Yes'
        },
        {
            id: 2,
            salvagePartPrefix: 'SAL',
            newPartPrefix: 'NEW',
            newPartNumber: '25137',
            newPartName: 'HOLLOW SCREW, M14X1.5-26',
            hsnCode: '73181500',
            hsnSlNo: '17',
            freeStock: 22,
            blockedQty: 0,
            allocatedStock: 2,
            pickedQty: 3,
            deviationStock: 4,
            computerStock: 31,
            git: 5,
            totalStock: 36,
            workshopStock: 22,
            vendorStock: 23,
            binLocation: 'A-01-4-A-04',
            binCode: '6',
            status: 'Active',
            active: 'Yes'
        }
    ];
    } else {
        sampleParts = [
        {
            id: 1,
            code: 'NP001',
            number: '11394',
            name: 'GASKET, JET, 12T8',
            category: 'ENGINE PARTS',
            supplier: 'VOLVO PARTS AB',
            supplierCode: 'VP001',
            supplierPrice: 4.00,
            coolPrice: 4.50,
            wtAverageCost: 4.70,
            sellingPrice: 8.75,
            freeStock: 215,
            totalStock: 215,
            binLocation: 'SMALL BIN D13F',
            binCode: '3293',
            hsnCode: '40163340',
            weight: 0.5,
            uom: 'PCS',
            leadTime: 31,
            movementType: 'slow',
            introductionDate: '2015-01-12',
            lastGrnDate: '2024-05-03',
            lastIssueDate: '2024-06-11',
            createdDate: '2015-01-12',
            lastModified: '2024-06-11',
            status: 'Active',
            active: 'Yes'
        },
        {
            id: 2,
            code: 'NP002',
            number: '11395',
            name: 'FILTER, OIL',
            category: 'ENGINE PARTS',
            supplier: 'CATERPILLAR INC',
            supplierCode: 'CAT001',
            supplierPrice: 10.00,
            coolPrice: 11.00,
            wtAverageCost: 11.50,
            sellingPrice: 12.50,
            freeStock: 89,
            totalStock: 89,
            binLocation: 'LARGE BIN A01',
            binCode: '1001',
            hsnCode: '84212300',
            weight: 1.2,
            uom: 'PCS',
            leadTime: 15,
            movementType: 'medium',
            introductionDate: '2016-03-15',
            lastGrnDate: '2024-04-20',
            lastIssueDate: '2024-06-05',
            createdDate: '2016-03-15',
            lastModified: '2024-06-05',
            status: 'Active',
            active: 'Yes'
        },
        {
            id: 3,
            code: 'SP001',
            number: '11396',
            name: 'SEAL, HYDRAULIC',
            category: 'HYDRAULIC PARTS',
            supplier: 'JOHN DEERE',
            supplierCode: 'JD001',
            supplierPrice: 12.00,
            coolPrice: 13.50,
            wtAverageCost: 14.00,
            sellingPrice: 15.25,
            freeStock: 156,
            totalStock: 156,
            binLocation: 'MEDIUM BIN B05',
            binCode: '2005',
            hsnCode: '40169300',
            weight: 0.3,
            uom: 'PCS',
            leadTime: 20,
            movementType: 'fast',
            introductionDate: '2017-08-10',
            lastGrnDate: '2024-06-01',
            lastIssueDate: '2024-06-10',
            createdDate: '2017-08-10',
            lastModified: '2024-06-10',
            status: 'Active',
            active: 'Yes'
        },
        // Additional sample data for pagination testing
        {
            id: 4,
            code: 'NP003',
            number: '11397',
            name: 'BEARING, MAIN',
            category: 'ENGINE PARTS',
            supplier: 'CUMMINS INC',
            supplierCode: 'CUM001',
            supplierPrice: 25.00,
            coolPrice: 27.50,
            wtAverageCost: 28.00,
            sellingPrice: 32.50,
            freeStock: 45,
            totalStock: 45,
            binLocation: 'SMALL BIN C12',
            binCode: '2012',
            hsnCode: '84821000',
            weight: 2.1,
            uom: 'PCS',
            leadTime: 25,
            movementType: 'medium',
            introductionDate: '2018-02-20',
            lastGrnDate: '2024-05-15',
            lastIssueDate: '2024-06-08',
            createdDate: '2018-02-20',
            lastModified: '2024-06-08',
            status: 'Active',
            active: 'Yes'
        },
        {
            id: 5,
            code: 'CP001',
            number: '11398',
            name: 'PISTON RING SET',
            category: 'ENGINE PARTS',
            supplier: 'MAHLE GMBH',
            supplierCode: 'MAH001',
            supplierPrice: 35.00,
            coolPrice: 38.50,
            wtAverageCost: 39.00,
            sellingPrice: 45.75,
            freeStock: 78,
            totalStock: 78,
            binLocation: 'MEDIUM BIN A08',
            binCode: '1008',
            hsnCode: '84099100',
            weight: 1.8,
            uom: 'SET',
            leadTime: 30,
            movementType: 'slow',
            introductionDate: '2019-05-12',
            lastGrnDate: '2024-04-28',
            lastIssueDate: '2024-05-22',
            createdDate: '2019-05-12',
            lastModified: '2024-05-22',
            status: 'Active',
            active: 'Yes'
        }
    ];
    }

    // Generate additional sample data to test pagination (20+ records)
    const additionalParts = [];
    for (let i = 6; i <= 25; i++) {
        const categories = ['ENGINE PARTS', 'HYDRAULIC PARTS', 'TRANSMISSION PARTS', 'ELECTRICAL PARTS', 'BRAKE PARTS'];
        const suppliers = ['VOLVO PARTS AB', 'CATERPILLAR INC', 'JOHN DEERE', 'CUMMINS INC', 'MAHLE GMBH', 'BOSCH GMBH', 'DENSO CORP'];
        const movements = ['slow', 'medium', 'fast'];
        const statuses = ['Active', 'Inactive', 'Discontinued'];

        additionalParts.push({
            id: i,
            code: `NP${String(i).padStart(3, '0')}`,
            number: `1139${i}`,
            name: `PART NAME ${i}`,
            category: categories[Math.floor(Math.random() * categories.length)],
            supplier: suppliers[Math.floor(Math.random() * suppliers.length)],
            supplierCode: `SUP${String(i).padStart(3, '0')}`,
            supplierPrice: Math.round((Math.random() * 50 + 5) * 100) / 100,
            coolPrice: Math.round((Math.random() * 55 + 6) * 100) / 100,
            wtAverageCost: Math.round((Math.random() * 60 + 7) * 100) / 100,
            sellingPrice: Math.round((Math.random() * 70 + 10) * 100) / 100,
            freeStock: Math.floor(Math.random() * 200 + 10),
            totalStock: Math.floor(Math.random() * 200 + 10),
            binLocation: `BIN ${String.fromCharCode(65 + Math.floor(Math.random() * 5))}${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`,
            binCode: String(Math.floor(Math.random() * 9000) + 1000),
            hsnCode: `${Math.floor(Math.random() * 90000000) + 10000000}`,
            weight: Math.round((Math.random() * 5 + 0.1) * 10) / 10,
            uom: Math.random() > 0.7 ? 'SET' : 'PCS',
            leadTime: Math.floor(Math.random() * 45 + 7),
            movementType: movements[Math.floor(Math.random() * movements.length)],
            introductionDate: `201${Math.floor(Math.random() * 9) + 5}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
            lastGrnDate: `2024-${String(Math.floor(Math.random() * 6) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
            lastIssueDate: `2024-${String(Math.floor(Math.random() * 6) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
            createdDate: `201${Math.floor(Math.random() * 9) + 5}-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
            lastModified: `2024-${String(Math.floor(Math.random() * 6) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
            status: statuses[Math.floor(Math.random() * statuses.length)],
            active: Math.random() > 0.1 ? 'Yes' : 'No'
        });
    }

    const allSampleParts = [...sampleParts, ...additionalParts];

    if (viewMode === 'list') {
        // Different table headers based on category
        let tableHeaders;

        if (category === 'salvage-parts') {
            tableHeaders = `
                <tr>
                    <th>Salvage Part Prefix</th>
                    <th>New Part Prefix</th>
                    <th>New Part Number</th>
                    <th>New Part Name</th>
                    <th>HSN Code</th>
                    <th>HSN Sl. No</th>
                    <th>Free Stock</th>
                    <th>Blocked Qty</th>
                    <th>Allocated Stock</th>
                    <th>Picked Qty</th>
                    <th>Total Stock</th>
                    <th>Bin Location</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            `;
        } else if (category === 'core-parts') {
            tableHeaders = `
                <tr>
                    <th>Prefix</th>
                    <th>Part Number</th>
                    <th>Part Description</th>
                    <th>Quantity</th>
                    <th>Is Mandatory?</th>
                    <th>% of Parts Consumption Estimated</th>
                    <th>% of Parts Consumption Actual</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            `;
        } else {
            tableHeaders = `
            <tr>
                <th>Part Code</th>
                <th>Part Number</th>
                <th>Part Name</th>
                <th>Type</th>
                <th>Category</th>
                <th>Supplier</th>
                <th>Supplier Code</th>
                <th>Supplier Price</th>
                <th>Selling Price</th>
                <th>Stock</th>
                <th>Bin Location</th>
                <th>UOM</th>
                <th>Lead Time</th>
                <th>Movement</th>
                <th>Created Date</th>
                <th>Last Modified</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        `;
        }

        return `
            <div class="parts-list-view">
                <div class="table-container">
                    <table class="data-table enhanced-table">
                        <thead>
                            ${tableHeaders}
                        </thead>
                        <tbody>
                            ${allSampleParts.map(part => {
                                if (category === 'salvage-parts') {
                                    return `
                                        <tr>
                                            <td><strong class="part-prefix">${part.salvagePartPrefix}</strong></td>
                                            <td><strong class="part-prefix">${part.newPartPrefix}</strong></td>
                                            <td class="part-number">${part.newPartNumber}</td>
                                            <td class="part-name" title="${part.newPartName}">${part.newPartName}</td>
                                            <td class="hsn-code">${part.hsnCode}</td>
                                            <td class="hsn-sl-no">${part.hsnSlNo}</td>
                                            <td><span class="stock-badge ${part.freeStock > 10 ? 'stock-good' : part.freeStock > 0 ? 'stock-medium' : 'stock-low'}">${part.freeStock}</span></td>
                                            <td><span class="stock-badge">${part.blockedQty}</span></td>
                                            <td><span class="stock-badge">${part.allocatedStock}</span></td>
                                            <td><span class="stock-badge">${part.pickedQty}</span></td>
                                            <td><span class="stock-badge total-stock">${part.totalStock}</span></td>
                                            <td class="bin-location" title="${part.binLocation}">${part.binLocation}</td>
                                            <td><span class="status-badge ${part.status === 'Active' ? 'status-active' : part.status === 'Inactive' ? 'status-inactive' : 'status-discontinued'}">${part.status}</span></td>
                                            <td class="actions-cell">
                                                <div class="action-buttons">
                                                    <button class="action-btn edit-btn" onclick="editPart('${category}', ${part.id})" title="Edit Part" aria-label="Edit ${part.newPartName}">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </button>
                                                    <button class="action-btn view-btn" onclick="viewPartDetails('${category}', ${part.id})" title="View Details" aria-label="View details for ${part.newPartName}">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                                                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </button>
                                                    <button class="action-btn copy-btn" onclick="copyPart('${category}', ${part.id})" title="Duplicate Part" aria-label="Duplicate ${part.newPartName}">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </button>
                                                    <button class="action-btn delete-btn" onclick="deletePart('${category}', ${part.id})" title="Delete Part" aria-label="Delete ${part.newPartName}">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                                                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2"/>
                                                            <line x1="10" y1="11" x2="10" y2="17" stroke="currentColor" stroke-width="2"/>
                                                            <line x1="14" y1="11" x2="14" y2="17" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `;
                                } else if (category === 'core-parts') {
                                    return `
                                        <tr>
                                            <td><strong class="part-prefix">${part.prefix}</strong></td>
                                            <td class="part-number">${part.partNumber}</td>
                                            <td class="part-description" title="${part.partDescription}">${part.partDescription}</td>
                                            <td class="quantity">${part.quantity}</td>
                                            <td><span class="mandatory-badge ${part.isMandatory === 'Yes' ? 'mandatory-yes' : 'mandatory-no'}">${part.isMandatory}</span></td>
                                            <td class="consumption-estimated">${part.partsConsumptionEstimated}%</td>
                                            <td class="consumption-actual">${part.partsConsumptionActual}%</td>
                                            <td><span class="status-badge ${part.status === 'Active' ? 'status-active' : part.status === 'Inactive' ? 'status-inactive' : 'status-discontinued'}">${part.status}</span></td>
                                            <td class="actions-cell">
                                                <div class="action-buttons">
                                                    <button class="action-btn edit-btn" onclick="editPart('${category}', ${part.id})" title="Edit Part" aria-label="Edit ${part.partDescription}">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                    </button>
                                                    <button class="action-btn view-btn" onclick="viewPartDetails('${category}', ${part.id})" title="View Details" aria-label="View details for ${part.partDescription}">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                                                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </button>
                                                    <button class="action-btn copy-btn" onclick="copyPart('${category}', ${part.id})" title="Duplicate Part" aria-label="Duplicate ${part.partDescription}">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </button>
                                                    <button class="action-btn delete-btn" onclick="deletePart('${category}', ${part.id})" title="Delete Part" aria-label="Delete ${part.partDescription}">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                                                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2"/>
                                                            <line x1="10" y1="11" x2="10" y2="17" stroke="currentColor" stroke-width="2"/>
                                                            <line x1="14" y1="11" x2="14" y2="17" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    `;
                                } else {
                                    return `
                                        <tr>
                                            <td><strong class="part-code">${part.code}</strong></td>
                                            <td class="part-number">${part.number}</td>
                                            <td class="part-name" title="${part.name}">${part.name}</td>
                                            <td><span class="part-type-badge type-${partType}">${partType.charAt(0).toUpperCase() + partType.slice(1)}</span></td>
                                            <td><span class="category-badge category-${part.category.toLowerCase().replace(/\s+/g, '-')}">${part.category}</span></td>
                                            <td class="supplier-name" title="${part.supplier}">${part.supplier}</td>
                                            <td class="supplier-code">${part.supplierCode}</td>
                                            <td class="price">₹${part.supplierPrice.toFixed(2)}</td>
                                            <td class="price">₹${part.sellingPrice.toFixed(2)}</td>
                                            <td><span class="stock-badge ${part.freeStock > 100 ? 'stock-good' : part.freeStock > 50 ? 'stock-medium' : 'stock-low'}">${part.freeStock}</span></td>
                                            <td class="bin-location" title="${part.binLocation}">${part.binLocation}</td>
                                            <td class="uom">${part.uom}</td>
                                            <td class="lead-time">${part.leadTime} days</td>
                                            <td><span class="movement-badge movement-${part.movementType}">${part.movementType}</span></td>
                                            <td class="date">${part.createdDate}</td>
                                            <td class="date">${part.lastModified}</td>
                                            <td><span class="status-badge ${part.status === 'Active' ? 'status-active' : part.status === 'Inactive' ? 'status-inactive' : 'status-discontinued'}">${part.status}</span></td>
                                            <td class="actions-cell">
                                                <div class="action-buttons">
                                                    <button class="action-btn edit-btn" onclick="editPart('${category}', ${part.id})" title="Edit Part" aria-label="Edit ${part.name}">
                                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </button>
                                            <button class="action-btn view-btn" onclick="viewPartDetails('${category}', ${part.id})" title="View Details" aria-label="View details for ${part.name}">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                                                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                                </svg>
                                            </button>
                                            <button class="action-btn copy-btn" onclick="copyPart('${category}', ${part.id})" title="Duplicate Part" aria-label="Duplicate ${part.name}">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                                                </svg>
                                            </button>
                                            <button class="action-btn delete-btn" onclick="deletePart('${category}', ${part.id})" title="Delete Part" aria-label="Delete ${part.name}">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2"/>
                                                    <line x1="10" y1="11" x2="10" y2="17" stroke="currentColor" stroke-width="2"/>
                                                    <line x1="14" y1="11" x2="14" y2="17" stroke="currentColor" stroke-width="2"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                    `;
                                }
                            }).join('')}
                        </tbody>
                    </table>
                </div>
                <div class="table-pagination">
                    <div class="pagination-info">
                        <span>Showing 1-10 of ${allSampleParts.length} records</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="15,18 9,12 15,6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                        <span class="pagination-pages">1 / ${Math.ceil(allSampleParts.length / 10)}</span>
                        <button class="pagination-btn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;
    } else if (viewMode === 'card') {
        return `
            <div class="parts-card-view">
                ${allSampleParts.map(part => `
                    <div class="part-card">
                        <div class="part-card-header">
                            <h5>${part.name}</h5>
                            <div class="part-badges">
                                <span class="part-code">${part.code}</span>
                                <span class="part-type-badge type-${partType}">${partType.charAt(0).toUpperCase() + partType.slice(1)}</span>
                                <span class="status-badge ${part.active === 'Yes' ? 'status-active' : 'status-inactive'}">${part.active}</span>
                            </div>
                        </div>
                        <div class="part-card-body">
                            <div class="card-info-grid">
                                <div class="info-item">
                                    <span class="info-label">Part Number:</span>
                                    <span class="info-value">${part.number}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Category:</span>
                                    <span class="info-value">${part.category}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Supplier:</span>
                                    <span class="info-value">${part.supplier}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Supplier Price:</span>
                                    <span class="info-value">₹${part.supplierPrice.toFixed(2)}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Selling Price:</span>
                                    <span class="info-value">₹${part.sellingPrice.toFixed(2)}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Stock:</span>
                                    <span class="info-value stock-badge ${part.freeStock > 100 ? 'stock-good' : part.freeStock > 50 ? 'stock-medium' : 'stock-low'}">${part.freeStock}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Bin Location:</span>
                                    <span class="info-value">${part.binLocation}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">UOM:</span>
                                    <span class="info-value">${part.uom}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Movement:</span>
                                    <span class="info-value movement-badge movement-${part.movementType}">${part.movementType}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Lead Time:</span>
                                    <span class="info-value">${part.leadTime} days</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Last GRN:</span>
                                    <span class="info-value">${part.lastGrnDate}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Last Issue:</span>
                                    <span class="info-value">${part.lastIssueDate}</span>
                                </div>
                            </div>
                        </div>
                        <div class="part-card-actions">
                            <button class="action-btn edit-btn" onclick="editPart('${category}', ${part.id})" title="Edit Part">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="action-btn view-btn" onclick="viewPartDetails('${category}', ${part.id})" title="View Details">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/>
                                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </button>
                            <button class="action-btn copy-btn" onclick="copyPart('${category}', ${part.id})" title="Duplicate Part">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </button>
                            <button class="action-btn delete-btn" onclick="deletePart('${category}', ${part.id})" title="Delete Part">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2"/>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2"/>
                                    <line x1="10" y1="11" x2="10" y2="17" stroke="currentColor" stroke-width="2"/>
                                    <line x1="14" y1="11" x2="14" y2="17" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    } else {
        return `
            <div class="parts-compact-view">
                ${sampleParts.map(part => `
                    <div class="part-compact-item">
                        <div class="part-compact-info">
                            <span class="part-name">${part.name}</span>
                            <span class="part-details">${part.code} | ${part.number} | ₹${part.price}</span>
                        </div>
                        <div class="part-compact-actions">
                            <button class="btn btn-sm btn-outline" onclick="editPart('${category}', ${part.id})">Edit</button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
}

/**
 * Set view mode for parts
 */
function setViewMode(category, mode) {
    const viewModeButtons = document.querySelectorAll('.view-mode-btn');
    viewModeButtons.forEach(btn => btn.classList.remove('active'));

    const activeButton = document.querySelector(`[data-mode="${mode}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }

    const dataContainer = document.getElementById(`partsDataContainer-${category}`);
    if (dataContainer) {
        dataContainer.innerHTML = getPartsListHTML(category, mode);
    }
}

/**
 * Filter parts by search term
 */
function filterParts(category, searchTerm) {
    // In a real application, this would filter the data and re-render
    console.log(`Filtering ${category} parts by: ${searchTerm}`);
}

/**
 * Filter parts by category
 */
function filterPartsByCategory(category, filterValue) {
    // In a real application, this would filter the data and re-render
    console.log(`Filtering ${category} parts by category: ${filterValue}`);
}

/**
 * Setup parts form submission
 */
function setupPartsFormSubmission(category) {
    const form = document.getElementById(`partsForm-${category}`);
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            handlePartsFormSubmission(category);
        });
    }
}

/**
 * Setup quantity calculation for salvage parts
 */
function setupSalvageQuantityCalculation(category) {
    const quantityFields = [
        'freeStock', 'blockedQty', 'allocatedStock', 'pickedQty',
        'deviationStock', 'computerStock', 'git', 'workshopStock', 'vendorStock'
    ];

    quantityFields.forEach(fieldName => {
        const field = document.getElementById(`${fieldName}-${category}`);
        if (field) {
            field.addEventListener('input', () => calculateTotalStock(category));
        }
    });

    // Initial calculation
    calculateTotalStock(category);
}

/**
 * Calculate total stock for salvage parts
 */
function calculateTotalStock(category) {
    const quantityFields = [
        'freeStock', 'blockedQty', 'allocatedStock', 'pickedQty',
        'deviationStock', 'computerStock', 'git', 'workshopStock', 'vendorStock'
    ];

    let total = 0;
    quantityFields.forEach(fieldName => {
        const field = document.getElementById(`${fieldName}-${category}`);
        if (field) {
            const value = parseFloat(field.value) || 0;
            total += value;
        }
    });

    const totalField = document.getElementById(`totalStock-${category}`);
    if (totalField) {
        totalField.value = total;
    }
}

/**
 * Handle parts form submission
 */
function handlePartsFormSubmission(category) {
    const form = document.getElementById(`partsForm-${category}`);
    if (!form) return;

    const formData = new FormData(form);
    const partData = Object.fromEntries(formData.entries());

    // Validate required fields based on category
    let requiredFields;
    if (category === 'salvage-parts') {
        requiredFields = ['salvagePartPrefix', 'newPartPrefix', 'newPartNumber', 'newPartName'];
    } else if (category === 'core-parts') {
        requiredFields = ['corePartPrefix', 'corePartNumber'];
    } else {
        requiredFields = ['partCode', 'partNumber', 'partName', 'category', 'supplier'];
    }
    const missingFields = requiredFields.filter(field => !partData[field] || partData[field].trim() === '');

    if (missingFields.length > 0) {
        showNotification(`Please fill in all required fields: ${missingFields.join(', ')}`, 'error');
        return;
    }

    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    addLoadingState(submitBtn);

    // Simulate API call
    setTimeout(() => {
        removeLoadingState(submitBtn);
        showNotification(`${category.replace('-', ' ')} saved successfully!`, 'success');
        console.log('Part data saved:', partData);

        // Optionally redirect back to view
        showPartsCategory(category, 'view');
    }, 1500);
}

/**
 * Clear parts form
 */
function clearPartsForm(category) {
    const form = document.getElementById(`partsForm-${category}`);
    if (form) {
        form.reset();
        showNotification('Form cleared', 'info');
    }
}

/**
 * Preview part
 */
function previewPart(category) {
    const form = document.getElementById(`partsForm-${category}`);
    if (!form) return;

    const formData = new FormData(form);
    const partData = Object.fromEntries(formData.entries());

    console.log('Part preview:', partData);
    showNotification('Preview functionality would show part details', 'info');
}

/**
 * Edit part
 */
function editPart(category, partId) {
    console.log(`Editing ${category} part with ID: ${partId}`);
    showNotification('Edit functionality would open edit form', 'info');
}

/**
 * View part details
 */
function viewPartDetails(category, partId) {
    console.log(`Viewing details for ${category} part with ID: ${partId}`);
    showNotification('View details functionality would open detailed view', 'info');
}

/**
 * Copy/Duplicate part
 */
function copyPart(category, partId) {
    console.log(`Copying ${category} part with ID: ${partId}`);
    showNotification('Part duplicated successfully', 'success');
    // In a real application, this would create a copy of the part with a new ID
}

/**
 * Delete part
 */
function deletePart(category, partId) {
    if (confirm('Are you sure you want to delete this part?')) {
        console.log(`Deleting ${category} part with ID: ${partId}`);
        showNotification('Part deleted successfully', 'success');
    }
}

/**
 * Save as draft
 */
function saveAsDraft(category) {
    const form = document.getElementById(`partsForm-${category}`);
    if (!form) return;

    const formData = new FormData(form);
    const partData = Object.fromEntries(formData.entries());

    // Add draft status
    partData.status = 'draft';
    partData.savedAt = new Date().toISOString();

    // Save to localStorage for demo purposes
    const drafts = JSON.parse(localStorage.getItem('partsDrafts') || '[]');
    const draftId = Date.now().toString();
    partData.draftId = draftId;
    partData.category = category;

    drafts.push(partData);
    localStorage.setItem('partsDrafts', JSON.stringify(drafts));

    showNotification('Part saved as draft successfully!', 'success');
    console.log('Draft saved:', partData);
}

/**
 * Load draft
 */
function loadDraft(draftId) {
    const drafts = JSON.parse(localStorage.getItem('partsDrafts') || '[]');
    const draft = drafts.find(d => d.draftId === draftId);

    if (draft) {
        showPartsCategory(draft.category, 'add');

        // Wait for form to load then populate
        setTimeout(() => {
            populateForm(draft.category, draft);
        }, 100);
    }
}

/**
 * Populate form with data - Enhanced for searchable dropdowns
 */
function populateForm(category, data) {
    const form = document.getElementById(`partsForm-${category}`);
    if (!form) return;

    Object.keys(data).forEach(key => {
        const field = form.querySelector(`[name="${key}"]`);
        if (field) {
            if (field.type === 'radio') {
                const radioButton = form.querySelector(`[name="${key}"][value="${data[key]}"]`);
                if (radioButton) radioButton.checked = true;
            } else if (field.type === 'checkbox') {
                field.checked = data[key] === 'true' || data[key] === true;
            } else if (field.tagName === 'SELECT') {
                // Handle both regular selects and searchable dropdowns
                field.value = data[key];

                // If this is a searchable dropdown, update the display
                const searchableDropdown = field.nextElementSibling;
                if (searchableDropdown && searchableDropdown.classList.contains('searchable-dropdown')) {
                    // Find the SearchableDropdown instance and update it
                    const option = Array.from(field.options).find(opt => opt.value === data[key]);
                    if (option) {
                        // Trigger a change event to update the searchable dropdown display
                        const changeEvent = new Event('change', { bubbles: true });
                        field.dispatchEvent(changeEvent);
                    }
                }
            } else {
                field.value = data[key];
            }
        }
    });

    showNotification('Form populated with saved data', 'info');
}

/**
 * Enhanced edit part function
 */
function editPart(category, partId) {
    // In a real application, this would fetch data from API
    let sampleData;

    if (category === 'salvage-parts') {
        sampleData = {
            salvagePartPrefix: 'SAL',
            newPartPrefix: 'NEW',
            newPartNumber: '183272',
            newPartName: 'NEEDLE ROLLER BUSHING',
            hsnCode: '84824000',
            hsnSlNo: '30',
            freeStock: '0',
            blockedQty: '0',
            allocatedStock: '0',
            pickedQty: '0',
            deviationStock: '0',
            computerStock: '0',
            git: '0',
            totalStock: '0',
            workshopStock: '0',
            vendorStock: '0',
            binLocationName: 'NO BIN',
            binLocationCode: '1',
            altBinLocationName: '',
            altBinLocationCode: '',
            introductionDate: '2015-01-12',
            lastGrnDate: '2015-01-10',
            firstDeliveryDate: '2015-01-11',
            lastIssueDate: '2015-01-12',
            lastDemandDate: '2015-01-13',
            lastStockCheckDate: '2015-01-14'
        };
    } else if (category === 'core-parts') {
        sampleData = {
            corePartPrefix: 'COR',
            corePartNumber: '85007673',
            newPartPrefix: 'NEW',
            newPartNumber: '21479533',
            newPartName: 'DZE T2',
            hsnCode: '84082020',
            hsnSlNo: '57',
            businessAreaName: 'BUS',
            businessAreaCode: '1',
            defaultBinName: 'NO BIN',
            defaultBinCode: '1',
            gitFromDealer: '0',
            computerStock: '1',
            gitToInternalWorkshop: '0',
            averageLeadTime: '6',
            coreRate: '10912.00',
            remarks: 'Sample core part for testing'
        };
    } else {
        sampleData = {
            partCode: 'NP001',
            partNumber: '11394',
            partName: 'GASKET, JET, 12T8',
            partPrefix: 'NEW',
            categoryName: 'ENGINE PARTS',
            supplierName: 'VOLVO PARTS AB',
            supplierPrice: '4.00',
            sellingPrice: '8.75',
            freeStock: '215',
            binLocationName: 'SMALL BIN D13F',
            binLocationCode: '3293',
            hsnCode: '40163340',
            weight: '0',
            uom: 'PCS',
            leadTime: '31'
        };
    }

    // Show add form
    showPartsCategory(category, 'add');

    // Wait for form to load then populate with existing data
    setTimeout(() => {
        // Initialize searchable dropdowns first
        const dynamicContent = document.getElementById('partsDynamicContent');
        if (dynamicContent) {
            initializeSearchableDropdowns(dynamicContent);
        }

        // Setup quantity calculation for salvage parts
        if (category === 'salvage-parts') {
            setupSalvageQuantityCalculation(category);
        }

        populateForm(category, sampleData);

        // Update form title to indicate editing
        const formHeader = document.querySelector('.parts-form-header h4');
        if (formHeader) {
            const categoryNames = {
                'new-parts': 'New Parts',
                'salvage-parts': 'Salvage Parts',
                'core-parts': 'Core Parts',
                'exchange-parts': 'Exchange Parts'
            };
            formHeader.textContent = `Edit ${categoryNames[category]}`;
        }

        // Update submit button text
        const submitBtn = document.querySelector(`#partsForm-${category} button[type="submit"]`);
        if (submitBtn) {
            submitBtn.textContent = `Update ${categoryNames[category] || category}`;
        }
    }, 100);
}

/**
 * Import/Export functionality - Enhanced with responsive modal
 */
function importExportParts(category) {
    const categoryNames = {
        'new-parts': 'New Parts',
        'salvage-parts': 'Salvage Parts',
        'core-parts': 'Core Parts',
        'exchange-parts': 'Exchange Parts'
    };

    const categoryName = categoryNames[category] || category;
    showImportExportModal(category, categoryName);
}

/**
 * Show Import/Export Modal with responsive design
 */
function showImportExportModal(category, categoryName) {
    // Remove existing modal if present
    const existingModal = document.querySelector('.import-export-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create modal HTML
    const modalHTML = `
        <div class="import-export-modal" id="importExportModal">
            <div class="import-export-modal-content">
                <div class="import-export-modal-header">
                    <h3 class="import-export-modal-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        Import/Export ${categoryName}
                    </h3>
                    <button class="import-export-modal-close" onclick="closeImportExportModal()">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                            <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>

                <div class="import-export-modal-body">
                    <div class="import-export-tabs">
                        <button class="import-export-tab active" onclick="switchImportExportTab('import', this)">
                            Import Data
                        </button>
                        <button class="import-export-tab" onclick="switchImportExportTab('export', this)">
                            Export Data
                        </button>
                        <button class="import-export-tab" onclick="switchImportExportTab('templates', this)">
                            Templates
                        </button>
                    </div>

                    <!-- Import Tab Content -->
                    <div class="import-export-tab-content active" id="importTab">
                        <div class="import-export-section">
                            <h4 class="import-export-section-title">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                    <polyline points="17,8 12,3 7,8" stroke="currentColor" stroke-width="2"/>
                                    <line x1="12" y1="3" x2="12" y2="15" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                Upload ${categoryName} Data
                            </h4>
                            <p class="import-export-section-description">
                                Upload a CSV or Excel file containing ${categoryName.toLowerCase()} data. Make sure your file follows the required format.
                            </p>

                            <div class="file-upload-area" onclick="document.getElementById('fileInput-${category}').click()">
                                <input type="file" id="fileInput-${category}" class="file-input" accept=".csv,.xlsx,.xls" onchange="handleFileUpload(this, '${category}')">
                                <div class="file-upload-icon">
                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                        <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
                                        <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
                                        <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </div>
                                <div class="file-upload-text">Click to upload or drag and drop</div>
                                <div class="file-upload-subtext">Supports CSV, Excel (.xlsx, .xls) files up to 10MB</div>
                            </div>

                            <div class="import-progress" id="importProgress-${category}">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progressFill-${category}"></div>
                                </div>
                                <div class="progress-text" id="progressText-${category}">Processing...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Export Tab Content -->
                    <div class="import-export-tab-content" id="exportTab">
                        <div class="import-export-section">
                            <h4 class="import-export-section-title">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                    <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                    <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                Export ${categoryName} Data
                            </h4>
                            <p class="import-export-section-description">
                                Export your ${categoryName.toLowerCase()} data in various formats for backup or analysis.
                            </p>

                            <div class="export-options-grid">
                                <div class="export-option" onclick="selectExportOption(this, 'csv')">
                                    <div class="template-icon">
                                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="template-title">CSV Format</div>
                                    <div class="template-description">Comma-separated values for spreadsheet applications</div>
                                </div>

                                <div class="export-option" onclick="selectExportOption(this, 'excel')">
                                    <div class="template-icon">
                                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="template-title">Excel Format</div>
                                    <div class="template-description">Microsoft Excel workbook with formatting</div>
                                </div>

                                <div class="export-option" onclick="selectExportOption(this, 'pdf')">
                                    <div class="template-icon">
                                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="template-title">PDF Report</div>
                                    <div class="template-description">Formatted report for printing and sharing</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Templates Tab Content -->
                    <div class="import-export-tab-content" id="templatesTab">
                        <div class="import-export-section">
                            <h4 class="import-export-section-title">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                    <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                    <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                Download Templates
                            </h4>
                            <p class="import-export-section-description">
                                Download pre-formatted templates to ensure your data import is successful.
                            </p>

                            <div class="template-download-grid">
                                <div class="template-card" onclick="downloadTemplate('${category}', 'basic')">
                                    <div class="template-icon">
                                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="template-title">Basic Template</div>
                                    <div class="template-description">Essential fields for ${categoryName.toLowerCase()}</div>
                                    <button class="btn btn-outline btn-sm">Download CSV</button>
                                </div>

                                <div class="template-card" onclick="downloadTemplate('${category}', 'detailed')">
                                    <div class="template-icon">
                                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2"/>
                                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="template-title">Detailed Template</div>
                                    <div class="template-description">Complete fields including pricing and inventory</div>
                                    <button class="btn btn-outline btn-sm">Download Excel</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="import-export-modal-actions">
                    <button class="btn btn-outline" onclick="closeImportExportModal()">Cancel</button>
                    <button class="btn btn-primary" onclick="executeImportExport('${category}')">Execute</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal with animation
    setTimeout(() => {
        const modal = document.getElementById('importExportModal');
        modal.classList.add('active');
    }, 10);

    // Setup drag and drop
    setupDragAndDrop(category);

    // Setup modal close on backdrop click
    setupModalBackdropClose();
}



/**
 * Close import/export modal - Enhanced
 */
function closeImportExportModal() {
    const modal = document.querySelector('.import-export-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

/**
 * Switch Import/Export tabs
 */
function switchImportExportTab(tabName, element) {
    // Remove active class from all tabs and content
    document.querySelectorAll('.import-export-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelectorAll('.import-export-tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // Add active class to selected tab and content
    if (element) {
        element.classList.add('active');
    }
    const tabContent = document.getElementById(tabName + 'Tab');
    if (tabContent) {
        tabContent.classList.add('active');
    }
}

/**
 * Handle file upload with progress
 */
function handleFileUpload(fileInput, category) {
    const file = fileInput.files[0];
    if (!file) return;

    const progressContainer = document.getElementById(`importProgress-${category}`);
    const progressFill = document.getElementById(`progressFill-${category}`);
    const progressText = document.getElementById(`progressText-${category}`);

    if (progressContainer) {
        progressContainer.classList.add('active');
        progressText.textContent = `Processing ${file.name}...`;

        // Simulate upload progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);

                progressText.textContent = 'Import completed successfully!';
                showNotification(`${file.name} imported successfully`, 'success');

                setTimeout(() => {
                    progressContainer.classList.remove('active');
                    progressFill.style.width = '0%';
                }, 2000);
            }
            progressFill.style.width = progress + '%';
        }, 200);
    }
}

/**
 * Select export option
 */
function selectExportOption(element, format) {
    // Remove selected class from all options
    document.querySelectorAll('.export-option').forEach(option => {
        option.classList.remove('selected');
    });

    // Add selected class to clicked option
    element.classList.add('selected');

    // Store selected format
    element.closest('.import-export-modal').setAttribute('data-export-format', format);
}

/**
 * Execute Import/Export action
 */
function executeImportExport(category) {
    const modal = document.querySelector('.import-export-modal');
    const activeTab = document.querySelector('.import-export-tab.active').textContent.trim();

    if (activeTab === 'Import Data') {
        const fileInput = document.getElementById(`fileInput-${category}`);
        if (fileInput.files.length > 0) {
            handleFileUpload(fileInput, category);
        } else {
            showNotification('Please select a file to import', 'warning');
        }
    } else if (activeTab === 'Export Data') {
        const format = modal.getAttribute('data-export-format') || 'csv';
        exportParts(category, format);
    } else {
        showNotification('Please select an action to execute', 'info');
    }
}

/**
 * Setup drag and drop functionality
 */
function setupDragAndDrop(category) {
    const uploadArea = document.querySelector('.file-upload-area');
    const fileInput = document.getElementById(`fileInput-${category}`);

    if (uploadArea && fileInput) {
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileUpload(fileInput, category);
            }
        });
    }
}

/**
 * Setup modal backdrop close
 */
function setupModalBackdropClose() {
    const modal = document.querySelector('.import-export-modal');
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeImportExportModal();
            }
        });

        // ESC key close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeImportExportModal();
            }
        });
    }
}

/**
 * Download template file
 */
function downloadTemplate(category, type) {
    const categoryNames = {
        'new-parts': 'New Parts',
        'salvage-parts': 'Salvage Parts',
        'core-parts': 'Core Parts',
        'exchange-parts': 'Exchange Parts'
    };

    const categoryName = categoryNames[category] || category;
    const fileName = `${categoryName.replace(' ', '_')}_${type}_template.csv`;

    showNotification(`Downloading ${type} template for ${categoryName}`, 'success');
    console.log(`Download template: ${fileName}`);
}

/**
 * Export parts data
 */
function exportParts(category, format = 'csv') {
    const categoryNames = {
        'new-parts': 'New Parts',
        'salvage-parts': 'Salvage Parts',
        'core-parts': 'Core Parts',
        'exchange-parts': 'Exchange Parts'
    };

    const categoryName = categoryNames[category] || category;
    const fileName = `${categoryName.replace(' ', '_')}_export.${format}`;

    showNotification(`Exporting ${categoryName} data as ${format.toUpperCase()}`, 'success');
    console.log(`Export: ${fileName}`);

    // Close modal after export
    setTimeout(() => {
        closeImportExportModal();
    }, 1500);
}

/**
 * ===== SEARCHABLE DROPDOWN COMPONENT =====
 */

/**
 * SearchableDropdown Class - Custom searchable dropdown component
 */
window.SearchableDropdown = class SearchableDropdown {
    constructor(selectElement, options = {}) {
        this.originalSelect = selectElement;
        this.options = {
            placeholder: options.placeholder || 'Search or select...',
            searchPlaceholder: options.searchPlaceholder || 'Type to search...',
            noResultsText: options.noResultsText || 'No results found',
            clearable: options.clearable !== false,
            searchable: options.searchable !== false,
            maxHeight: options.maxHeight || 300,
            ...options
        };

        this.isOpen = false;
        this.selectedValue = '';
        this.selectedText = '';
        this.filteredOptions = [];
        this.highlightedIndex = -1;

        this.init();
    }

    init() {
        this.createDropdownStructure();
        this.populateOptions();
        this.bindEvents();
        this.updateDisplay();

        // Hide original select
        this.originalSelect.style.display = 'none';

        // Insert dropdown after original select
        this.originalSelect.parentNode.insertBefore(this.dropdown, this.originalSelect.nextSibling);
    }

    createDropdownStructure() {
        this.dropdown = document.createElement('div');
        this.dropdown.className = 'searchable-dropdown';

        // Copy classes from original select
        if (this.originalSelect.className) {
            this.dropdown.className += ' ' + this.originalSelect.className.replace('form-select', '');
        }

        // Copy attributes
        if (this.originalSelect.disabled) {
            this.dropdown.classList.add('disabled');
        }

        if (this.originalSelect.required) {
            this.dropdown.setAttribute('data-required', 'true');
        }

        this.dropdown.innerHTML = `
            <button type="button" class="searchable-dropdown-toggle" aria-haspopup="listbox" aria-expanded="false">
                <span class="searchable-dropdown-text placeholder">${this.options.placeholder}</span>
                <div class="searchable-dropdown-icons">
                    ${this.options.clearable ? `
                        <button type="button" class="searchable-dropdown-clear" style="display: none;" aria-label="Clear selection">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                                <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    ` : ''}
                    <div class="searchable-dropdown-arrow">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                </div>
            </button>
            <div class="searchable-dropdown-menu">
                ${this.options.searchable ? `
                    <input type="text" class="searchable-dropdown-search" placeholder="${this.options.searchPlaceholder}" autocomplete="off">
                ` : ''}
                <div class="searchable-dropdown-options" role="listbox"></div>
            </div>
        `;

        // Get references to elements
        this.toggleButton = this.dropdown.querySelector('.searchable-dropdown-toggle');
        this.text = this.dropdown.querySelector('.searchable-dropdown-text');
        this.clearBtn = this.dropdown.querySelector('.searchable-dropdown-clear');
        this.arrow = this.dropdown.querySelector('.searchable-dropdown-arrow');
        this.menu = this.dropdown.querySelector('.searchable-dropdown-menu');
        this.searchInput = this.dropdown.querySelector('.searchable-dropdown-search');
        this.optionsContainer = this.dropdown.querySelector('.searchable-dropdown-options');
    }

    populateOptions() {
        this.allOptions = [];
        const options = this.originalSelect.querySelectorAll('option');

        options.forEach((option, index) => {
            if (option.value !== '') {
                this.allOptions.push({
                    value: option.value,
                    text: option.textContent.trim(),
                    selected: option.selected,
                    disabled: option.disabled,
                    index: index
                });
            }
        });

        this.filteredOptions = [...this.allOptions];

        // Set initial selection
        const selectedOption = this.allOptions.find(opt => opt.selected);
        if (selectedOption) {
            this.selectedValue = selectedOption.value;
            this.selectedText = selectedOption.text;
        }
    }

    bindEvents() {
        // Toggle dropdown
        this.toggleButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (!this.dropdown.classList.contains('disabled')) {
                this.toggleDropdown();
            }
        });

        // Clear selection
        if (this.clearBtn) {
            this.clearBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.clearSelection();
            });
        }

        // Search input
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.filterOptions(e.target.value);
            });

            this.searchInput.addEventListener('keydown', (e) => {
                this.handleKeydown(e);
            });
        }

        // Options container
        this.optionsContainer.addEventListener('click', (e) => {
            const option = e.target.closest('.searchable-dropdown-option');
            if (option && !option.classList.contains('disabled')) {
                const value = option.getAttribute('data-value');
                this.selectOption(value);
            }
        });

        // Keyboard navigation
        this.toggleButton.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        // Close on outside click
        document.addEventListener('click', (e) => {
            if (!this.dropdown.contains(e.target)) {
                this.close();
            }
        });

        // Close on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
                this.toggle.focus();
            }
        });
    }

    toggleDropdown() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        if (this.dropdown.classList.contains('disabled')) return;

        this.isOpen = true;
        this.toggleButton.classList.add('open');
        this.toggleButton.setAttribute('aria-expanded', 'true');
        this.menu.classList.add('open');

        // Reset search
        if (this.searchInput) {
            this.searchInput.value = '';
            this.filterOptions('');
            setTimeout(() => {
                this.searchInput.focus();
            }, 100);
        }

        // Highlight selected option
        this.highlightSelectedOption();

        // Position dropdown
        this.positionDropdown();
    }

    close() {
        this.isOpen = false;
        this.toggleButton.classList.remove('open');
        this.toggleButton.setAttribute('aria-expanded', 'false');
        this.menu.classList.remove('open');
        this.highlightedIndex = -1;
    }

    positionDropdown() {
        const rect = this.dropdown.getBoundingClientRect();
        const spaceBelow = window.innerHeight - rect.bottom;
        const spaceAbove = rect.top;

        if (spaceBelow < 200 && spaceAbove > spaceBelow) {
            // Open upward
            this.menu.style.top = 'auto';
            this.menu.style.bottom = '100%';
            this.menu.style.borderTop = '1px solid var(--primary-teal)';
            this.menu.style.borderBottom = 'none';
            this.menu.style.borderRadius = 'var(--border-radius-sm) var(--border-radius-sm) 0 0';
        } else {
            // Open downward (default)
            this.menu.style.top = '100%';
            this.menu.style.bottom = 'auto';
            this.menu.style.borderTop = 'none';
            this.menu.style.borderBottom = '1px solid var(--primary-teal)';
            this.menu.style.borderRadius = '0 0 var(--border-radius-sm) var(--border-radius-sm)';
        }
    }

    filterOptions(searchTerm) {
        const term = searchTerm.toLowerCase().trim();

        if (!term) {
            this.filteredOptions = [...this.allOptions];
        } else {
            this.filteredOptions = this.allOptions.filter(option =>
                option.text.toLowerCase().includes(term) ||
                option.value.toLowerCase().includes(term)
            );
        }

        this.renderOptions();
        this.highlightedIndex = -1;
    }

    renderOptions() {
        if (this.filteredOptions.length === 0) {
            this.optionsContainer.innerHTML = `
                <div class="searchable-dropdown-no-results">${this.options.noResultsText}</div>
            `;
            return;
        }

        this.optionsContainer.innerHTML = this.filteredOptions.map((option, index) => {
            const isSelected = option.value === this.selectedValue;
            const searchTerm = this.searchInput ? this.searchInput.value.toLowerCase() : '';
            let displayText = option.text;

            // Highlight search term
            if (searchTerm && searchTerm.length > 0) {
                const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                displayText = option.text.replace(regex, '<span class="highlight">$1</span>');
            }

            return `
                <button type="button"
                        class="searchable-dropdown-option ${isSelected ? 'selected' : ''}"
                        data-value="${option.value}"
                        data-index="${index}"
                        role="option"
                        aria-selected="${isSelected}">
                    ${displayText}
                </button>
            `;
        }).join('');
    }

    selectOption(value) {
        const option = this.allOptions.find(opt => opt.value === value);
        if (!option) return;

        this.selectedValue = value;
        this.selectedText = option.text;

        // Update original select
        this.originalSelect.value = value;

        // Trigger change event on original select
        const changeEvent = new Event('change', { bubbles: true });
        this.originalSelect.dispatchEvent(changeEvent);

        this.updateDisplay();
        this.close();
        this.toggleButton.focus();
    }

    clearSelection() {
        this.selectedValue = '';
        this.selectedText = '';
        this.originalSelect.value = '';

        // Trigger change event
        const changeEvent = new Event('change', { bubbles: true });
        this.originalSelect.dispatchEvent(changeEvent);

        this.updateDisplay();
        this.toggleButton.focus();
    }

    updateDisplay() {
        if (this.selectedText) {
            this.text.textContent = this.selectedText;
            this.text.classList.remove('placeholder');
            if (this.clearBtn) {
                this.clearBtn.style.display = 'flex';
            }
        } else {
            this.text.textContent = this.options.placeholder;
            this.text.classList.add('placeholder');
            if (this.clearBtn) {
                this.clearBtn.style.display = 'none';
            }
        }
    }

    highlightSelectedOption() {
        if (this.selectedValue) {
            const selectedOption = this.optionsContainer.querySelector(`[data-value="${this.selectedValue}"]`);
            if (selectedOption) {
                selectedOption.scrollIntoView({ block: 'nearest' });
                this.highlightedIndex = parseInt(selectedOption.getAttribute('data-index'));
            }
        }
    }

    handleKeydown(e) {
        if (!this.isOpen && (e.key === 'ArrowDown' || e.key === 'ArrowUp' || e.key === 'Enter' || e.key === ' ')) {
            e.preventDefault();
            this.open();
            return;
        }

        if (!this.isOpen) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.highlightNext();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.highlightPrevious();
                break;
            case 'Enter':
                e.preventDefault();
                if (this.highlightedIndex >= 0 && this.filteredOptions[this.highlightedIndex]) {
                    this.selectOption(this.filteredOptions[this.highlightedIndex].value);
                }
                break;
            case 'Escape':
                e.preventDefault();
                this.close();
                this.toggleButton.focus();
                break;
            case 'Tab':
                this.close();
                break;
        }
    }

    highlightNext() {
        if (this.filteredOptions.length === 0) return;

        this.highlightedIndex = Math.min(this.highlightedIndex + 1, this.filteredOptions.length - 1);
        this.updateHighlight();
    }

    highlightPrevious() {
        if (this.filteredOptions.length === 0) return;

        this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
        this.updateHighlight();
    }

    updateHighlight() {
        // Remove previous highlight
        this.optionsContainer.querySelectorAll('.searchable-dropdown-option').forEach(option => {
            option.classList.remove('highlighted');
        });

        // Add highlight to current option
        if (this.highlightedIndex >= 0) {
            const options = this.optionsContainer.querySelectorAll('.searchable-dropdown-option');
            if (options[this.highlightedIndex]) {
                options[this.highlightedIndex].classList.add('highlighted');
                options[this.highlightedIndex].scrollIntoView({ block: 'nearest' });
            }
        }
    }

    setValue(value) {
        const option = this.allOptions.find(opt => opt.value === value);
        if (option) {
            this.selectedValue = value;
            this.selectedText = option.text;
            this.originalSelect.value = value;
            this.updateDisplay();
        }
    }

    getValue() {
        return this.selectedValue;
    }

    disable() {
        this.dropdown.classList.add('disabled');
        this.originalSelect.disabled = true;
    }

    enable() {
        this.dropdown.classList.remove('disabled');
        this.originalSelect.disabled = false;
    }

    destroy() {
        // Remove event listeners and DOM elements
        this.dropdown.remove();
        this.originalSelect.style.display = '';
    }
}

/**
 * Initialize searchable dropdowns for form selects
 */
function initializeSearchableDropdowns(container = document) {
    const selects = container.querySelectorAll('select.form-select');

    selects.forEach((select, index) => {
        // Skip if already converted
        if (select.style.display === 'none' && select.nextElementSibling?.classList.contains('searchable-dropdown')) {
            return;
        }

        try {
            // Create searchable dropdown
            new SearchableDropdown(select, {
                placeholder: select.getAttribute('data-placeholder') || 'Search or select...',
                searchPlaceholder: 'Type to search...',
                noResultsText: 'No results found',
                clearable: !select.required,
                searchable: true
            });
        } catch (error) {
            console.error(`Error creating searchable dropdown for select ${index}:`, error);
        }
    });
}

/**
 * Download template
 */
function downloadTemplate(category) {
    // In a real application, this would generate and download an actual template
    showNotification('Template download started', 'info');
    console.log(`Downloading template for ${category}`);
}

/**
 * Handle file import
 */
function handleFileImport(category, fileInput) {
    const file = fileInput.files[0];
    if (!file) return;

    showNotification(`Importing ${file.name}...`, 'info');

    // Simulate import process
    setTimeout(() => {
        showNotification('Import completed successfully!', 'success');
        console.log(`Imported file: ${file.name} for category: ${category}`);
        closeImportExportModal();
    }, 2000);
}

/**
 * Export parts
 */
function exportParts(category) {
    const format = document.getElementById(`exportFormat-${category}`).value;
    const categoryFilter = document.getElementById(`exportCategory-${category}`).value;
    const supplierFilter = document.getElementById(`exportSupplier-${category}`).value;

    showNotification(`Exporting ${category} data as ${format.toUpperCase()}...`, 'info');

    // Simulate export process
    setTimeout(() => {
        showNotification('Export completed successfully!', 'success');
        console.log(`Exported ${category} data:`, { format, categoryFilter, supplierFilter });
        closeImportExportModal();
    }, 1500);
}

/**
 * Export current view
 */
function exportCurrentView(category) {
    showNotification('Exporting current view...', 'info');

    setTimeout(() => {
        showNotification('Current view exported successfully!', 'success');
        console.log(`Exported current view for ${category}`);
        closeImportExportModal();
    }, 1000);
}

/**
 * View part details
 */
function viewPartDetails(category, partId) {
    // In a real application, this would fetch detailed data from API
    const categoryNames = {
        'new-parts': 'New Parts',
        'salvage-parts': 'Salvage Parts',
        'core-parts': 'Core Parts',
        'exchange-parts': 'Exchange Parts'
    };

    const categoryName = categoryNames[category] || category;

    const detailsHTML = `
        <div class="part-details-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h4>${categoryName} Details - Part ID: ${partId}</h4>
                    <button class="modal-close" onclick="closePartDetailsModal()">&times;</button>
                </div>

                <div class="modal-body">
                    <div class="details-grid">
                        <div class="details-section">
                            <h5>Basic Information</h5>
                            <div class="details-row">
                                <span class="detail-label">Part Code:</span>
                                <span class="detail-value">NP001</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Part Number:</span>
                                <span class="detail-value">11394</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Part Name:</span>
                                <span class="detail-value">GASKET, JET, 12T8</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Active:</span>
                                <span class="detail-value status-badge status-active">Yes</span>
                            </div>
                        </div>

                        <div class="details-section">
                            <h5>Supplier Information</h5>
                            <div class="details-row">
                                <span class="detail-label">Supplier:</span>
                                <span class="detail-value">VOLVO PARTS AB</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Supplier Code:</span>
                                <span class="detail-value">VP001</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Category:</span>
                                <span class="detail-value">ENGINE PARTS</span>
                            </div>
                        </div>

                        <div class="details-section">
                            <h5>Pricing Information</h5>
                            <div class="details-row">
                                <span class="detail-label">Supplier Price:</span>
                                <span class="detail-value">₹4.00</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Cool Price:</span>
                                <span class="detail-value">₹4.50</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Wt. Average Cost:</span>
                                <span class="detail-value">₹4.70</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Selling Price:</span>
                                <span class="detail-value">₹8.75</span>
                            </div>
                        </div>

                        <div class="details-section">
                            <h5>Stock Information</h5>
                            <div class="details-row">
                                <span class="detail-label">Free Stock:</span>
                                <span class="detail-value stock-badge stock-good">215</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Total Stock:</span>
                                <span class="detail-value">215</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Bin Location:</span>
                                <span class="detail-value">SMALL BIN D13F (3293)</span>
                            </div>
                        </div>

                        <div class="details-section">
                            <h5>Physical Details</h5>
                            <div class="details-row">
                                <span class="detail-label">Weight:</span>
                                <span class="detail-value">0.5 Kg</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">UOM:</span>
                                <span class="detail-value">PCS</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Lead Time:</span>
                                <span class="detail-value">31 Days</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Movement Type:</span>
                                <span class="detail-value movement-badge movement-slow">Slow</span>
                            </div>
                        </div>

                        <div class="details-section">
                            <h5>Dates</h5>
                            <div class="details-row">
                                <span class="detail-label">Introduction Date:</span>
                                <span class="detail-value">2015-01-12</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Last GRN Date:</span>
                                <span class="detail-value">2024-05-03</span>
                            </div>
                            <div class="details-row">
                                <span class="detail-label">Last Issue Date:</span>
                                <span class="detail-value">2024-06-11</span>
                            </div>
                        </div>
                    </div>

                    <div class="details-actions">
                        <button class="btn btn-primary" onclick="editPart('${category}', ${partId})">Edit Part</button>
                        <button class="btn btn-secondary" onclick="printPartDetails('${category}', ${partId})">Print Details</button>
                        <button class="btn btn-outline" onclick="closePartDetailsModal()">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = detailsHTML;
    modalContainer.className = 'modal-overlay';
    document.body.appendChild(modalContainer);
}

/**
 * Close part details modal
 */
function closePartDetailsModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

/**
 * Print part details
 */
function printPartDetails(category, partId) {
    showNotification('Printing part details...', 'info');
    console.log(`Printing details for ${category} part ID: ${partId}`);
}

/**
 * Setup New Parts Master functionality
 */
function setupNewPartsMaster() {
    setupNewPartsTabNavigation();
    setupNewPartsFormHandlers();
    setupStockManagement();
    setupImportExportFunctionality();
}

/**
 * Setup tab navigation for New Parts Master
 */
function setupNewPartsTabNavigation() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Update active tab button
            tabButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Update active tab content
            tabContents.forEach(content => content.classList.remove('active'));
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });
}

/**
 * Setup form handlers for New Parts Master
 */
function setupNewPartsFormHandlers() {
    // Main form submission
    const newPartsForm = document.getElementById('newPartsForm');
    if (newPartsForm) {
        newPartsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleNewPartSubmission();
        });
    }

    // Clear form button
    const clearFormBtn = document.getElementById('clearFormBtn');
    if (clearFormBtn) {
        clearFormBtn.addEventListener('click', function() {
            clearNewPartsForm();
        });
    }

    // Preview button
    const previewBtn = document.getElementById('previewBtn');
    if (previewBtn) {
        previewBtn.addEventListener('click', function() {
            previewNewPart();
        });
    }

    // Category and supplier change handlers
    const categorySelect = document.getElementById('categoryName');
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            updateCategoryCode(this.value);
        });
    }

    const supplierSelect = document.getElementById('supplierName');
    if (supplierSelect) {
        supplierSelect.addEventListener('change', function() {
            updateSupplierCode(this.value);
        });
    }

    // Physical count change handler for stock reconciliation
    const physicalCountInput = document.getElementById('physicalCount');
    if (physicalCountInput) {
        physicalCountInput.addEventListener('input', function() {
            calculateStockVariance();
        });
    }

    // Auto-calculate total stock
    setupStockCalculations();
}

/**
 * Handle new part form submission
 */
function handleNewPartSubmission() {
    const formData = new FormData(document.getElementById('newPartsForm'));
    const partData = Object.fromEntries(formData.entries());

    // Validate required fields
    if (!validateNewPartForm(partData)) {
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('#newPartsForm button[type="submit"]');
    addLoadingState(submitBtn);

    // Simulate API call
    setTimeout(() => {
        removeLoadingState(submitBtn);
        showNotification('New part saved successfully!', 'success');
        console.log('Part data saved:', partData);

        // Optionally clear form or redirect
        // clearNewPartsForm();
    }, 1500);
}

/**
 * Validate new part form
 */
function validateNewPartForm(data) {
    const requiredFields = ['hsnCode', 'supplierName', 'categoryName', 'partNumber', 'partName'];
    const missingFields = [];

    requiredFields.forEach(field => {
        if (!data[field] || data[field].trim() === '') {
            missingFields.push(field);
        }
    });

    if (missingFields.length > 0) {
        showNotification(`Please fill in all required fields: ${missingFields.join(', ')}`, 'error');
        return false;
    }

    return true;
}

/**
 * Clear new parts form
 */
function clearNewPartsForm() {
    const form = document.getElementById('newPartsForm');
    if (form) {
        form.reset();
        showNotification('Form cleared', 'info');
    }
}

/**
 * Preview new part data
 */
function previewNewPart() {
    const formData = new FormData(document.getElementById('newPartsForm'));
    const partData = Object.fromEntries(formData.entries());

    // Create preview modal or display
    console.log('Part preview:', partData);
    showNotification('Preview functionality - check console for data', 'info');
}

/**
 * Update category code based on selection
 */
function updateCategoryCode(categoryName) {
    const categoryCodeInput = document.getElementById('categoryCode');
    if (categoryCodeInput) {
        const codes = {
            'ENGINE PARTS': 'ENG001',
            'HYDRAULIC PARTS': 'HYD001',
            'ELECTRICAL PARTS': 'ELE001',
            'TRANSMISSION PARTS': 'TRA001'
        };
        categoryCodeInput.value = codes[categoryName] || '';
    }
}

/**
 * Update supplier code based on selection
 */
function updateSupplierCode(supplierName) {
    const supplierCodeInput = document.getElementById('supplierCode');
    if (supplierCodeInput) {
        const codes = {
            'VOLVO PARTS AB': 'VOL001',
            'CATERPILLAR INC': 'CAT001',
            'JOHN DEERE': 'JD001'
        };
        supplierCodeInput.value = codes[supplierName] || '';
    }
}

/**
 * Setup stock calculations
 */
function setupStockCalculations() {
    const stockInputs = document.querySelectorAll('.quantity-item input[type="number"]');

    stockInputs.forEach(input => {
        if (input.id !== 'totalStock' && input.id !== 'computerStock') {
            input.addEventListener('input', function() {
                calculateTotalStock();
            });
        }
    });
}

/**
 * Calculate total stock
 */
function calculateTotalStock() {
    const freeStock = parseInt(document.getElementById('freeStock')?.value || 0);
    const allocatedStock = parseInt(document.getElementById('allocatedStock')?.value || 0);
    const workshopStock = parseInt(document.getElementById('workshopStock')?.value || 0);
    const vendorStock = parseInt(document.getElementById('vendorStock')?.value || 0);

    const total = freeStock + allocatedStock + workshopStock + vendorStock;

    const totalStockInput = document.getElementById('totalStock');
    const computerStockInput = document.getElementById('computerStock');

    if (totalStockInput) totalStockInput.value = total;
    if (computerStockInput) computerStockInput.value = total;
}

/**
 * Calculate stock variance for reconciliation
 */
function calculateStockVariance() {
    const physicalCount = parseInt(document.getElementById('physicalCount')?.value || 0);
    const systemCount = parseInt(document.getElementById('systemCount')?.value || 0);
    const variance = physicalCount - systemCount;

    const varianceInput = document.getElementById('variance');
    if (varianceInput) {
        varianceInput.value = variance;

        // Color code the variance
        if (variance > 0) {
            varianceInput.style.color = '#059669'; // Green for positive
        } else if (variance < 0) {
            varianceInput.style.color = '#dc2626'; // Red for negative
        } else {
            varianceInput.style.color = '#6b7280'; // Gray for zero
        }
    }
}

/**
 * Setup stock management functionality
 */
function setupStockManagement() {
    // Reconcile stock button
    const reconcileBtn = document.getElementById('reconcileStockBtn');
    if (reconcileBtn) {
        reconcileBtn.addEventListener('click', function() {
            handleStockReconciliation();
        });
    }

    // Generate report button
    const generateReportBtn = document.getElementById('generateReportBtn');
    if (generateReportBtn) {
        generateReportBtn.addEventListener('click', function() {
            generateStockReport();
        });
    }

    // Update stock button
    const updateStockBtn = document.getElementById('updateStockBtn');
    if (updateStockBtn) {
        updateStockBtn.addEventListener('click', function() {
            updateStockLevels();
        });
    }
}

/**
 * Handle stock reconciliation
 */
function handleStockReconciliation() {
    const physicalCount = document.getElementById('physicalCount')?.value;
    const reason = document.getElementById('reconciliationReason')?.value;

    if (!physicalCount) {
        showNotification('Please enter physical count', 'error');
        return;
    }

    if (!reason && document.getElementById('variance')?.value != 0) {
        showNotification('Please provide reason for variance', 'error');
        return;
    }

    // Simulate reconciliation process
    showNotification('Processing stock reconciliation...', 'info');

    setTimeout(() => {
        showNotification('Stock reconciliation completed successfully', 'success');
        console.log('Stock reconciled:', { physicalCount, reason });
    }, 1500);
}

/**
 * Generate stock report
 */
function generateStockReport() {
    showNotification('Generating stock report...', 'info');

    setTimeout(() => {
        showNotification('Stock report generated successfully', 'success');
        console.log('Stock report generated');
    }, 1000);
}

/**
 * Update stock levels
 */
function updateStockLevels() {
    showNotification('Updating stock levels...', 'info');

    setTimeout(() => {
        showNotification('Stock levels updated successfully', 'success');
        console.log('Stock levels updated');
    }, 1000);
}

/**
 * Setup import/export functionality
 */
function setupImportExportFunctionality() {
    setupImportHandlers();
    setupExportHandlers();
}

/**
 * Setup import handlers
 */
function setupImportHandlers() {
    // Template download buttons
    const downloadPartsTemplateBtn = document.getElementById('downloadPartsTemplateBtn');
    if (downloadPartsTemplateBtn) {
        downloadPartsTemplateBtn.addEventListener('click', function() {
            downloadTemplate('parts');
        });
    }

    const downloadPricingTemplateBtn = document.getElementById('downloadPricingTemplateBtn');
    if (downloadPricingTemplateBtn) {
        downloadPricingTemplateBtn.addEventListener('click', function() {
            downloadTemplate('pricing');
        });
    }

    const downloadBinTemplateBtn = document.getElementById('downloadBinTemplateBtn');
    if (downloadBinTemplateBtn) {
        downloadBinTemplateBtn.addEventListener('click', function() {
            downloadTemplate('bin');
        });
    }

    // Upload buttons
    const uploadPartsBtn = document.getElementById('uploadPartsBtn');
    if (uploadPartsBtn) {
        uploadPartsBtn.addEventListener('click', function() {
            document.getElementById('partsFileInput')?.click();
        });
    }

    const uploadPricingBtn = document.getElementById('uploadPricingBtn');
    if (uploadPricingBtn) {
        uploadPricingBtn.addEventListener('click', function() {
            document.getElementById('pricingFileInput')?.click();
        });
    }

    const uploadBinBtn = document.getElementById('uploadBinBtn');
    if (uploadBinBtn) {
        uploadBinBtn.addEventListener('click', function() {
            document.getElementById('binFileInput')?.click();
        });
    }

    // File input handlers
    const partsFileInput = document.getElementById('partsFileInput');
    if (partsFileInput) {
        partsFileInput.addEventListener('change', function(e) {
            handleFileUpload(e.target.files[0], 'parts');
        });
    }

    const pricingFileInput = document.getElementById('pricingFileInput');
    if (pricingFileInput) {
        pricingFileInput.addEventListener('change', function(e) {
            handleFileUpload(e.target.files[0], 'pricing');
        });
    }

    const binFileInput = document.getElementById('binFileInput');
    if (binFileInput) {
        binFileInput.addEventListener('change', function(e) {
            handleFileUpload(e.target.files[0], 'bin');
        });
    }
}

/**
 * Setup export handlers
 */
function setupExportHandlers() {
    const exportPartsBtn = document.getElementById('exportPartsBtn');
    if (exportPartsBtn) {
        exportPartsBtn.addEventListener('click', function() {
            handleDataExport('parts');
        });
    }

    const exportStockBtn = document.getElementById('exportStockBtn');
    if (exportStockBtn) {
        exportStockBtn.addEventListener('click', function() {
            handleDataExport('stock');
        });
    }
}

/**
 * Download template file
 */
function downloadTemplate(type) {
    const templates = {
        'parts': 'Parts_Import_Template.xlsx',
        'pricing': 'Pricing_Update_Template.xlsx',
        'bin': 'Bin_Location_Template.xlsx'
    };

    showNotification(`Downloading ${templates[type]}...`, 'info');

    // Simulate file download
    setTimeout(() => {
        showNotification(`${templates[type]} downloaded successfully`, 'success');
        console.log(`Template downloaded: ${templates[type]}`);
    }, 500);
}

/**
 * Handle file upload
 */
function handleFileUpload(file, type) {
    if (!file) return;

    // Validate file type
    const allowedTypes = ['.xlsx', '.xls', '.csv'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
        showNotification('Please upload a valid Excel or CSV file', 'error');
        return;
    }

    // Show import status
    const importStatus = document.getElementById('importStatus');
    const progressFill = document.getElementById('progressFill');
    const statusText = document.getElementById('statusText');
    const statusDetails = document.getElementById('statusDetails');

    if (importStatus) {
        importStatus.style.display = 'block';
        statusText.textContent = `Processing ${file.name}...`;
        statusDetails.textContent = 'Validating file format and data...';

        // Simulate upload progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);

                statusText.textContent = 'Import completed successfully';
                statusDetails.textContent = `${type} data has been imported and validated.`;
                showNotification(`${file.name} imported successfully`, 'success');

                setTimeout(() => {
                    importStatus.style.display = 'none';
                    progressFill.style.width = '0%';
                }, 3000);
            }
            progressFill.style.width = progress + '%';
        }, 200);
    }

    console.log(`File uploaded: ${file.name}, Type: ${type}`);
}

/**
 * Handle data export
 */
function handleDataExport(type) {
    const exportFormat = document.getElementById('exportFormat')?.value || 'xlsx';
    const category = document.getElementById('exportCategory')?.value;
    const supplier = document.getElementById('exportSupplier')?.value;

    const filters = {
        category: category || 'All Categories',
        supplier: supplier || 'All Suppliers',
        format: exportFormat
    };

    showNotification(`Exporting ${type} data...`, 'info');

    setTimeout(() => {
        showNotification(`${type} data exported successfully as ${exportFormat.toUpperCase()}`, 'success');
        console.log(`Data exported:`, { type, filters });
    }, 1500);
}

/**
 * Setup button interactions
 */
function setupButtonInteractions() {
    const buttons = document.querySelectorAll('.btn');

    buttons.forEach(button => {
        button.addEventListener('click', function() {
            handleButtonClick(this);
        });

        // Add ripple effect
        button.addEventListener('mousedown', function(e) {
            createRippleEffect(this, e);
        });
    });
}

/**
 * Handle button click events
 */
function handleButtonClick(button) {
    const buttonText = button.textContent.trim();

    // Add loading state
    addLoadingState(button);

    // Simulate API call or action
    setTimeout(() => {
        removeLoadingState(button);
        showNotification(`${buttonText} action completed`, 'success');
    }, 1000);

    console.log(`Button clicked: ${buttonText}`);
}

/**
 * Create ripple effect on button click
 */
function createRippleEffect(button, event) {
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
    `;

    button.style.position = 'relative';
    button.style.overflow = 'hidden';
    button.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Add loading state to button
 */
function addLoadingState(button) {
    button.disabled = true;
    button.dataset.originalText = button.textContent;
    button.textContent = 'Loading...';
    button.classList.add('loading');
}

/**
 * Remove loading state from button
 */
function removeLoadingState(button) {
    button.disabled = false;
    button.textContent = button.dataset.originalText;
    button.classList.remove('loading');
}

/**
 * Setup form interactions
 */
function setupFormInteractions() {
    const selects = document.querySelectorAll('.option-select');
    const checkboxes = document.querySelectorAll('.option-checkbox');

    selects.forEach(select => {
        select.addEventListener('change', function() {
            saveUserPreference(this.name || 'setting', this.value);
            showNotification('Setting updated', 'info');
        });
    });

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            saveUserPreference(this.name || 'setting', this.checked);
            showNotification('Setting updated', 'info');
        });
    });

    // Setup enhanced radio button interactions
    setupEnhancedRadioButtons();

    // Initialize searchable dropdowns on page load
    initializeSearchableDropdowns();
}

/**
 * Setup enhanced radio button functionality - Prevents duplication
 */
function setupEnhancedRadioButtons() {
    // Handle radio button changes for both regular and enhanced radio buttons
    document.addEventListener('change', function(e) {
        if (e.target.type === 'radio') {
            handleRadioButtonChange(e.target);
        }
    });

    // Handle label clicks for enhanced radio buttons
    document.addEventListener('click', function(e) {
        const radioLabel = e.target.closest('.radio-label, .enhanced-radio-label');
        if (radioLabel) {
            const radioInput = radioLabel.querySelector('input[type="radio"]');
            if (radioInput && !radioInput.checked) {
                radioInput.checked = true;
                radioInput.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }
    });

    // Initialize existing radio buttons on page load
    initializeExistingRadioButtons();
}

/**
 * Handle radio button state changes - Prevents duplication
 */
function handleRadioButtonChange(radioInput) {
    const name = radioInput.name;
    const container = radioInput.closest('.radio-group, .enhanced-radio-group, .checkbox-group');

    if (container) {
        // Remove active class from all radio labels in the same group
        const allLabels = container.querySelectorAll('.radio-label, .enhanced-radio-label, .checkbox-label');
        allLabels.forEach(label => {
            label.classList.remove('active');
        });

        // Add active class to the selected radio label
        const selectedLabel = radioInput.closest('.radio-label, .enhanced-radio-label, .checkbox-label');
        if (selectedLabel) {
            selectedLabel.classList.add('active');
        }
    }

    // Trigger custom event for form validation
    radioInput.dispatchEvent(new CustomEvent('radioChanged', {
        bubbles: true,
        detail: { name: name, value: radioInput.value }
    }));
}

/**
 * Initialize existing radio buttons on page load
 */
function initializeExistingRadioButtons() {
    const checkedRadios = document.querySelectorAll('input[type="radio"]:checked');
    checkedRadios.forEach(radio => {
        handleRadioButtonChange(radio);
    });
}

/**
 * Update status indicators with real-time data
 */
function updateStatusIndicators() {
    const statusNumbers = document.querySelectorAll('.status-number');

    statusNumbers.forEach(element => {
        const currentValue = parseInt(element.textContent);
        const newValue = Math.floor(Math.random() * 50) + 1; // Simulate dynamic data

        animateNumber(element, currentValue, newValue, 1000);
    });
}

/**
 * Animate number changes
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

/**
 * Load user preferences
 */
function loadUserPreferences() {
    const preferences = getStoredPreferences();

    Object.keys(preferences).forEach(key => {
        const element = document.querySelector(`[name="${key}"]`);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = preferences[key];
            } else {
                element.value = preferences[key];
            }
        }
    });
}

/**
 * Save user preference
 */
function saveUserPreference(key, value) {
    const preferences = getStoredPreferences();
    preferences[key] = value;
    localStorage.setItem('remanERPPreferences', JSON.stringify(preferences));
}

/**
 * Get stored preferences
 */
function getStoredPreferences() {
    const stored = localStorage.getItem('remanERPPreferences');
    return stored ? JSON.parse(stored) : {};
}

/**
 * Initialize report filters
 */
function initializeReportFilters() {
    // Placeholder for report filter functionality
    console.log('Report filters initialized');
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        background: var(--primary-teal);
        color: white;
        border-radius: 8px;
        box-shadow: var(--shadow-md);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease-in-out;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

/**
 * Setup accessibility features
 */
function setupAccessibility() {
    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });

    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });

    // Skip link removed for space optimization
}

/**
 * Utility function to debounce function calls
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Utility function to throttle function calls
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .keyboard-navigation *:focus {
        outline: 2px solid var(--secondary-bright-blue) !important;
        outline-offset: 2px !important;
    }

    .loading {
        opacity: 0.7;
        cursor: not-allowed;
    }

    /* Core Parts specific styling */
    .mandatory-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        text-transform: uppercase;
    }

    .mandatory-yes {
        background-color: #fee2e2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .mandatory-no {
        background-color: #f3f4f6;
        color: #6b7280;
        border: 1px solid #d1d5db;
    }

    .consumption-estimated,
    .consumption-actual {
        font-weight: 500;
        text-align: center;
    }

    .part-description {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .quantity {
        text-align: center;
        font-weight: 500;
    }

    /* Core Parts form styling */
    .core-parts .form-section {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .core-parts .section-title {
        color: #1e40af;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #3b82f6;
    }
`;
document.head.appendChild(style);